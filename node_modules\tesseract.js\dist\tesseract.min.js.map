{"version": 3, "file": "tesseract.min.js", "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAmB,UAAID,IAEvBD,EAAgB,UAAIC,GACrB,CATD,CASGK,MAAM,+PCWTH,EAAOD,QAnBP,WAEI,MAAsB,oBAAXK,QAAoD,WAA1BC,EAAOD,OAAOE,UAAgD,aAAxBF,OAAOE,QAAQC,QAKnE,oBAAZD,SAAuD,WAA5BD,EAAOC,QAAQE,YAA2BF,QAAQE,SAASC,WAKxE,gCAAdC,UAAS,YAAAL,EAATK,aAAyD,iBAAxBA,UAAUC,WAA0BD,UAAUC,UAAUC,QAAQ,aAAe,CAK/H,uPCXA,IAAIC,EAAW,SAAUd,GACvB,aAEA,IAGIe,EAHAC,EAAKC,OAAOC,UACZC,EAASH,EAAGI,eACZC,EAAiBJ,OAAOI,gBAAkB,SAAUC,EAAKC,EAAKC,GAAQF,EAAIC,GAAOC,EAAKC,KAAO,EAE7FC,EAA4B,mBAAXC,OAAwBA,OAAS,CAAC,EACnDC,EAAiBF,EAAQG,UAAY,aACrCC,EAAsBJ,EAAQK,eAAiB,kBAC/CC,EAAoBN,EAAQO,aAAe,gBAE/C,SAAS/B,EAAOoB,EAAKC,EAAKE,GAOxB,OANAR,OAAOI,eAAeC,EAAKC,EAAK,CAC9BE,MAAOA,EACPS,YAAY,EACZC,cAAc,EACdC,UAAU,IAELd,EAAIC,EACb,CACA,IAEErB,EAAO,CAAC,EAAG,GACb,CAAE,MAAOmC,GACPnC,EAAS,SAASoB,EAAKC,EAAKE,GAC1B,OAAOH,EAAIC,GAAOE,CACpB,CACF,CAEA,SAASa,EAAKC,EAASC,EAASpC,EAAMqC,GAEpC,IAAIC,EAAiBF,GAAWA,EAAQtB,qBAAqByB,EAAYH,EAAUG,EAC/EC,EAAY3B,OAAO4B,OAAOH,EAAexB,WACzC4B,EAAU,IAAIC,EAAQN,GAAe,IAMzC,OAFApB,EAAeuB,EAAW,UAAW,CAAEnB,MAAOuB,EAAiBT,EAASnC,EAAM0C,KAEvEF,CACT,CAaA,SAASK,EAASC,EAAI5B,EAAK6B,GACzB,IACE,MAAO,CAAE3C,KAAM,SAAU2C,IAAKD,EAAGE,KAAK9B,EAAK6B,GAC7C,CAAE,MAAOd,GACP,MAAO,CAAE7B,KAAM,QAAS2C,IAAKd,EAC/B,CACF,CAlBArC,EAAQsC,KAAOA,EAoBf,IAAIe,EAAyB,iBACzBC,EAAyB,iBACzBC,EAAoB,YACpBC,EAAoB,YAIpBC,EAAmB,CAAC,EAMxB,SAASd,IAAa,CACtB,SAASe,IAAqB,CAC9B,SAASC,IAA8B,CAIvC,IAAIC,EAAoB,CAAC,EACzB1D,EAAO0D,EAAmBhC,GAAgB,WACxC,OAAOiC,IACT,IAEA,IAAIC,EAAW7C,OAAO8C,eAClBC,EAA0BF,GAAYA,EAASA,EAASG,EAAO,MAC/DD,GACAA,IAA4BhD,GAC5BG,EAAOiC,KAAKY,EAAyBpC,KAGvCgC,EAAoBI,GAGtB,IAAIE,EAAKP,EAA2BzC,UAClCyB,EAAUzB,UAAYD,OAAO4B,OAAOe,GAgBtC,SAASO,EAAsBjD,GAC7B,CAAC,OAAQ,QAAS,UAAUkD,SAAQ,SAASC,GAC3CnE,EAAOgB,EAAWmD,GAAQ,SAASlB,GACjC,OAAOU,KAAKS,QAAQD,EAAQlB,EAC9B,GACF,GACF,CA+BA,SAASoB,EAAc3B,EAAW4B,GAChC,SAASC,EAAOJ,EAAQlB,EAAKuB,EAASC,GACpC,IAAIC,EAAS3B,EAASL,EAAUyB,GAASzB,EAAWO,GACpD,GAAoB,UAAhByB,EAAOpE,KAEJ,CACL,IAAIqE,EAASD,EAAOzB,IAChB1B,EAAQoD,EAAOpD,MACnB,OAAIA,GACiB,WAAjBnB,EAAOmB,IACPN,EAAOiC,KAAK3B,EAAO,WACd+C,EAAYE,QAAQjD,EAAMqD,SAASC,MAAK,SAAStD,GACtDgD,EAAO,OAAQhD,EAAOiD,EAASC,EACjC,IAAG,SAAStC,GACVoC,EAAO,QAASpC,EAAKqC,EAASC,EAChC,IAGKH,EAAYE,QAAQjD,GAAOsD,MAAK,SAASC,GAI9CH,EAAOpD,MAAQuD,EACfN,EAAQG,EACV,IAAG,SAASI,GAGV,OAAOR,EAAO,QAASQ,EAAOP,EAASC,EACzC,GACF,CAzBEA,EAAOC,EAAOzB,IA0BlB,CAEA,IAAI+B,EAgCJ7D,EAAewC,KAAM,UAAW,CAAEpC,MA9BlC,SAAiB4C,EAAQlB,GACvB,SAASgC,IACP,OAAO,IAAIX,GAAY,SAASE,EAASC,GACvCF,EAAOJ,EAAQlB,EAAKuB,EAASC,EAC/B,GACF,CAEA,OAAOO,EAaLA,EAAkBA,EAAgBH,KAChCI,EAGAA,GACEA,GACR,GAKF,CA0BA,SAASnC,EAAiBT,EAASnC,EAAM0C,GACvC,IAAIsC,EAAQ/B,EAEZ,OAAO,SAAgBgB,EAAQlB,GAC7B,GAAIiC,IAAU7B,EACZ,MAAM,IAAI8B,MAAM,gCAGlB,GAAID,IAAU5B,EAAmB,CAC/B,GAAe,UAAXa,EACF,MAAMlB,EAKR,OAAOmC,GACT,CAKA,IAHAxC,EAAQuB,OAASA,EACjBvB,EAAQK,IAAMA,IAED,CACX,IAAIoC,EAAWzC,EAAQyC,SACvB,GAAIA,EAAU,CACZ,IAAIC,EAAiBC,EAAoBF,EAAUzC,GACnD,GAAI0C,EAAgB,CAClB,GAAIA,IAAmB/B,EAAkB,SACzC,OAAO+B,CACT,CACF,CAEA,GAAuB,SAAnB1C,EAAQuB,OAGVvB,EAAQ4C,KAAO5C,EAAQ6C,MAAQ7C,EAAQK,SAElC,GAAuB,UAAnBL,EAAQuB,OAAoB,CACrC,GAAIe,IAAU/B,EAEZ,MADA+B,EAAQ5B,EACFV,EAAQK,IAGhBL,EAAQ8C,kBAAkB9C,EAAQK,IAEpC,KAA8B,WAAnBL,EAAQuB,QACjBvB,EAAQ+C,OAAO,SAAU/C,EAAQK,KAGnCiC,EAAQ7B,EAER,IAAIqB,EAAS3B,EAASV,EAASnC,EAAM0C,GACrC,GAAoB,WAAhB8B,EAAOpE,KAAmB,CAO5B,GAJA4E,EAAQtC,EAAQgD,KACZtC,EACAF,EAEAsB,EAAOzB,MAAQM,EACjB,SAGF,MAAO,CACLhC,MAAOmD,EAAOzB,IACd2C,KAAMhD,EAAQgD,KAGlB,CAA2B,UAAhBlB,EAAOpE,OAChB4E,EAAQ5B,EAGRV,EAAQuB,OAAS,QACjBvB,EAAQK,IAAMyB,EAAOzB,IAEzB,CACF,CACF,CAMA,SAASsC,EAAoBF,EAAUzC,GACrC,IAAIiD,EAAajD,EAAQuB,OACrBA,EAASkB,EAAS1D,SAASkE,GAC/B,GAAI1B,IAAWtD,EAOb,OAHA+B,EAAQyC,SAAW,KAGA,UAAfQ,GAA0BR,EAAS1D,SAAiB,SAGtDiB,EAAQuB,OAAS,SACjBvB,EAAQK,IAAMpC,EACd0E,EAAoBF,EAAUzC,GAEP,UAAnBA,EAAQuB,SAMK,WAAf0B,IACFjD,EAAQuB,OAAS,QACjBvB,EAAQK,IAAM,IAAI6C,UAChB,oCAAsCD,EAAa,aAN5CtC,EAYb,IAAImB,EAAS3B,EAASoB,EAAQkB,EAAS1D,SAAUiB,EAAQK,KAEzD,GAAoB,UAAhByB,EAAOpE,KAIT,OAHAsC,EAAQuB,OAAS,QACjBvB,EAAQK,IAAMyB,EAAOzB,IACrBL,EAAQyC,SAAW,KACZ9B,EAGT,IAAIwC,EAAOrB,EAAOzB,IAElB,OAAM8C,EAOFA,EAAKH,MAGPhD,EAAQyC,EAASW,YAAcD,EAAKxE,MAGpCqB,EAAQqD,KAAOZ,EAASa,QAQD,WAAnBtD,EAAQuB,SACVvB,EAAQuB,OAAS,OACjBvB,EAAQK,IAAMpC,GAUlB+B,EAAQyC,SAAW,KACZ9B,GANEwC,GA3BPnD,EAAQuB,OAAS,QACjBvB,EAAQK,IAAM,IAAI6C,UAAU,oCAC5BlD,EAAQyC,SAAW,KACZ9B,EA+BX,CAqBA,SAAS4C,EAAaC,GACpB,IAAIC,EAAQ,CAAEC,OAAQF,EAAK,IAEvB,KAAKA,IACPC,EAAME,SAAWH,EAAK,IAGpB,KAAKA,IACPC,EAAMG,WAAaJ,EAAK,GACxBC,EAAMI,SAAWL,EAAK,IAGxBzC,KAAK+C,WAAWC,KAAKN,EACvB,CAEA,SAASO,EAAcP,GACrB,IAAI3B,EAAS2B,EAAMQ,YAAc,CAAC,EAClCnC,EAAOpE,KAAO,gBACPoE,EAAOzB,IACdoD,EAAMQ,WAAanC,CACrB,CAEA,SAAS7B,EAAQN,GAIfoB,KAAK+C,WAAa,CAAC,CAAEJ,OAAQ,SAC7B/D,EAAY2B,QAAQiC,EAAcxC,MAClCA,KAAKmD,OAAM,EACb,CA8BA,SAAS/C,EAAOgD,GACd,GAAIA,EAAU,CACZ,IAAIC,EAAiBD,EAASrF,GAC9B,GAAIsF,EACF,OAAOA,EAAe9D,KAAK6D,GAG7B,GAA6B,mBAAlBA,EAASd,KAClB,OAAOc,EAGT,IAAKE,MAAMF,EAASG,QAAS,CAC3B,IAAIC,GAAK,EAAGlB,EAAO,SAASA,IAC1B,OAASkB,EAAIJ,EAASG,QACpB,GAAIjG,EAAOiC,KAAK6D,EAAUI,GAGxB,OAFAlB,EAAK1E,MAAQwF,EAASI,GACtBlB,EAAKL,MAAO,EACLK,EAOX,OAHAA,EAAK1E,MAAQV,EACboF,EAAKL,MAAO,EAELK,CACT,EAEA,OAAOA,EAAKA,KAAOA,CACrB,CACF,CAGA,MAAO,CAAEA,KAAMb,EACjB,CAGA,SAASA,IACP,MAAO,CAAE7D,MAAOV,EAAW+E,MAAM,EACnC,CA8MA,OAnnBApC,EAAkBxC,UAAYyC,EAC9BtC,EAAe6C,EAAI,cAAe,CAAEzC,MAAOkC,EAA4BxB,cAAc,IACrFd,EACEsC,EACA,cACA,CAAElC,MAAOiC,EAAmBvB,cAAc,IAE5CuB,EAAkB4D,YAAcpH,EAC9ByD,EACA3B,EACA,qBAaFhC,EAAQuH,oBAAsB,SAASC,GACrC,IAAIC,EAAyB,mBAAXD,GAAyBA,EAAOE,YAClD,QAAOD,IACHA,IAAS/D,GAG2B,uBAAnC+D,EAAKH,aAAeG,EAAKE,MAEhC,EAEA3H,EAAQ4H,KAAO,SAASJ,GAQtB,OAPIvG,OAAO4G,eACT5G,OAAO4G,eAAeL,EAAQ7D,IAE9B6D,EAAOM,UAAYnE,EACnBzD,EAAOsH,EAAQxF,EAAmB,sBAEpCwF,EAAOtG,UAAYD,OAAO4B,OAAOqB,GAC1BsD,CACT,EAMAxH,EAAQ+H,MAAQ,SAAS5E,GACvB,MAAO,CAAE2B,QAAS3B,EACpB,EAqEAgB,EAAsBI,EAAcrD,WACpChB,EAAOqE,EAAcrD,UAAWY,GAAqB,WACnD,OAAO+B,IACT,IACA7D,EAAQuE,cAAgBA,EAKxBvE,EAAQgI,MAAQ,SAASzF,EAASC,EAASpC,EAAMqC,EAAa+B,QACxC,IAAhBA,IAAwBA,EAAcyD,SAE1C,IAAIC,EAAO,IAAI3D,EACbjC,EAAKC,EAASC,EAASpC,EAAMqC,GAC7B+B,GAGF,OAAOxE,EAAQuH,oBAAoB/E,GAC/B0F,EACAA,EAAK/B,OAAOpB,MAAK,SAASF,GACxB,OAAOA,EAAOiB,KAAOjB,EAAOpD,MAAQyG,EAAK/B,MAC3C,GACN,EAsKAhC,EAAsBD,GAEtBhE,EAAOgE,EAAIlC,EAAmB,aAO9B9B,EAAOgE,EAAItC,GAAgB,WACzB,OAAOiC,IACT,IAEA3D,EAAOgE,EAAI,YAAY,WACrB,MAAO,oBACT,IAiCAlE,EAAQmI,KAAO,SAASC,GACtB,IAAIC,EAASpH,OAAOmH,GAChBD,EAAO,GACX,IAAK,IAAI5G,KAAO8G,EACdF,EAAKtB,KAAKtF,GAMZ,OAJA4G,EAAKG,UAIE,SAASnC,IACd,KAAOgC,EAAKf,QAAQ,CAClB,IAAI7F,EAAM4G,EAAKI,MACf,GAAIhH,KAAO8G,EAGT,OAFAlC,EAAK1E,MAAQF,EACb4E,EAAKL,MAAO,EACLK,CAEX,CAMA,OADAA,EAAKL,MAAO,EACLK,CACT,CACF,EAoCAnG,EAAQiE,OAASA,EAMjBlB,EAAQ7B,UAAY,CAClBwG,YAAa3E,EAEbiE,MAAO,SAASwB,GAcd,GAbA3E,KAAK4E,KAAO,EACZ5E,KAAKsC,KAAO,EAGZtC,KAAK6B,KAAO7B,KAAK8B,MAAQ5E,EACzB8C,KAAKiC,MAAO,EACZjC,KAAK0B,SAAW,KAEhB1B,KAAKQ,OAAS,OACdR,KAAKV,IAAMpC,EAEX8C,KAAK+C,WAAWxC,QAAQ0C,IAEnB0B,EACH,IAAK,IAAIb,KAAQ9D,KAEQ,MAAnB8D,EAAKe,OAAO,IACZvH,EAAOiC,KAAKS,KAAM8D,KACjBR,OAAOQ,EAAKgB,MAAM,MACrB9E,KAAK8D,GAAQ5G,EAIrB,EAEA6H,KAAM,WACJ/E,KAAKiC,MAAO,EAEZ,IACI+C,EADYhF,KAAK+C,WAAW,GACLG,WAC3B,GAAwB,UAApB8B,EAAWrI,KACb,MAAMqI,EAAW1F,IAGnB,OAAOU,KAAKiF,IACd,EAEAlD,kBAAmB,SAASmD,GAC1B,GAAIlF,KAAKiC,KACP,MAAMiD,EAGR,IAAIjG,EAAUe,KACd,SAASmF,EAAOC,EAAKC,GAYnB,OAXAtE,EAAOpE,KAAO,QACdoE,EAAOzB,IAAM4F,EACbjG,EAAQqD,KAAO8C,EAEXC,IAGFpG,EAAQuB,OAAS,OACjBvB,EAAQK,IAAMpC,KAGNmI,CACZ,CAEA,IAAK,IAAI7B,EAAIxD,KAAK+C,WAAWQ,OAAS,EAAGC,GAAK,IAAKA,EAAG,CACpD,IAAId,EAAQ1C,KAAK+C,WAAWS,GACxBzC,EAAS2B,EAAMQ,WAEnB,GAAqB,SAAjBR,EAAMC,OAIR,OAAOwC,EAAO,OAGhB,GAAIzC,EAAMC,QAAU3C,KAAK4E,KAAM,CAC7B,IAAIU,EAAWhI,EAAOiC,KAAKmD,EAAO,YAC9B6C,EAAajI,EAAOiC,KAAKmD,EAAO,cAEpC,GAAI4C,GAAYC,EAAY,CAC1B,GAAIvF,KAAK4E,KAAOlC,EAAME,SACpB,OAAOuC,EAAOzC,EAAME,UAAU,GACzB,GAAI5C,KAAK4E,KAAOlC,EAAMG,WAC3B,OAAOsC,EAAOzC,EAAMG,WAGxB,MAAO,GAAIyC,GACT,GAAItF,KAAK4E,KAAOlC,EAAME,SACpB,OAAOuC,EAAOzC,EAAME,UAAU,OAG3B,KAAI2C,EAMT,MAAM,IAAI/D,MAAM,0CALhB,GAAIxB,KAAK4E,KAAOlC,EAAMG,WACpB,OAAOsC,EAAOzC,EAAMG,WAKxB,CACF,CACF,CACF,EAEAb,OAAQ,SAASrF,EAAM2C,GACrB,IAAK,IAAIkE,EAAIxD,KAAK+C,WAAWQ,OAAS,EAAGC,GAAK,IAAKA,EAAG,CACpD,IAAId,EAAQ1C,KAAK+C,WAAWS,GAC5B,GAAId,EAAMC,QAAU3C,KAAK4E,MACrBtH,EAAOiC,KAAKmD,EAAO,eACnB1C,KAAK4E,KAAOlC,EAAMG,WAAY,CAChC,IAAI2C,EAAe9C,EACnB,KACF,CACF,CAEI8C,IACU,UAAT7I,GACS,aAATA,IACD6I,EAAa7C,QAAUrD,GACvBA,GAAOkG,EAAa3C,aAGtB2C,EAAe,MAGjB,IAAIzE,EAASyE,EAAeA,EAAatC,WAAa,CAAC,EAIvD,OAHAnC,EAAOpE,KAAOA,EACdoE,EAAOzB,IAAMA,EAETkG,GACFxF,KAAKQ,OAAS,OACdR,KAAKsC,KAAOkD,EAAa3C,WAClBjD,GAGFI,KAAKyF,SAAS1E,EACvB,EAEA0E,SAAU,SAAS1E,EAAQ+B,GACzB,GAAoB,UAAhB/B,EAAOpE,KACT,MAAMoE,EAAOzB,IAcf,MAXoB,UAAhByB,EAAOpE,MACS,aAAhBoE,EAAOpE,KACTqD,KAAKsC,KAAOvB,EAAOzB,IACM,WAAhByB,EAAOpE,MAChBqD,KAAKiF,KAAOjF,KAAKV,IAAMyB,EAAOzB,IAC9BU,KAAKQ,OAAS,SACdR,KAAKsC,KAAO,OACa,WAAhBvB,EAAOpE,MAAqBmG,IACrC9C,KAAKsC,KAAOQ,GAGPlD,CACT,EAEA8F,OAAQ,SAAS7C,GACf,IAAK,IAAIW,EAAIxD,KAAK+C,WAAWQ,OAAS,EAAGC,GAAK,IAAKA,EAAG,CACpD,IAAId,EAAQ1C,KAAK+C,WAAWS,GAC5B,GAAId,EAAMG,aAAeA,EAGvB,OAFA7C,KAAKyF,SAAS/C,EAAMQ,WAAYR,EAAMI,UACtCG,EAAcP,GACP9C,CAEX,CACF,EAEA,MAAS,SAAS+C,GAChB,IAAK,IAAIa,EAAIxD,KAAK+C,WAAWQ,OAAS,EAAGC,GAAK,IAAKA,EAAG,CACpD,IAAId,EAAQ1C,KAAK+C,WAAWS,GAC5B,GAAId,EAAMC,SAAWA,EAAQ,CAC3B,IAAI5B,EAAS2B,EAAMQ,WACnB,GAAoB,UAAhBnC,EAAOpE,KAAkB,CAC3B,IAAIgJ,EAAS5E,EAAOzB,IACpB2D,EAAcP,EAChB,CACA,OAAOiD,CACT,CACF,CAIA,MAAM,IAAInE,MAAM,wBAClB,EAEAoE,cAAe,SAASxC,EAAUf,EAAYE,GAa5C,OAZAvC,KAAK0B,SAAW,CACd1D,SAAUoC,EAAOgD,GACjBf,WAAYA,EACZE,QAASA,GAGS,SAAhBvC,KAAKQ,SAGPR,KAAKV,IAAMpC,GAGN0C,CACT,GAOKzD,CAET,CAvtBe,CA4tBK,WAALM,cAAgBL,EAAOD,QAAU,CAAC,GAGjD,IACE0J,mBAAqB5I,CACvB,CAAE,MAAO6I,GAWmB,gCAAfC,WAAU,YAAAtJ,EAAVsJ,aACTA,WAAWF,mBAAqB5I,EAEhC+I,SAAS,IAAK,yBAAdA,CAAwC/I,EAE5C,iRCvvBAgJ,EAAA,kBAAA9J,CAAA,MAAAA,EAAA,GAAAgB,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAI,gBAAA,SAAAC,EAAAC,EAAAC,GAAAF,EAAAC,GAAAC,EAAAC,KAAA,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAA/B,EAAAoB,EAAAC,EAAAE,GAAA,OAAAR,OAAAI,eAAAC,EAAAC,EAAA,CAAAE,MAAAA,EAAAS,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAd,EAAAC,EAAA,KAAArB,EAAA,aAAAmC,GAAAnC,EAAA,SAAAoB,EAAAC,EAAAE,GAAA,OAAAH,EAAAC,GAAAE,CAAA,WAAAa,EAAAC,EAAAC,EAAApC,EAAAqC,GAAA,IAAAC,EAAAF,GAAAA,EAAAtB,qBAAAyB,EAAAH,EAAAG,EAAAC,EAAA3B,OAAA4B,OAAAH,EAAAxB,WAAA4B,EAAA,IAAAC,EAAAN,GAAA,WAAApB,EAAAuB,EAAA,WAAAnB,MAAAuB,EAAAT,EAAAnC,EAAA0C,KAAAF,CAAA,UAAAK,EAAAC,EAAA5B,EAAA6B,GAAA,WAAA3C,KAAA,SAAA2C,IAAAD,EAAAE,KAAA9B,EAAA6B,GAAA,OAAAd,GAAA,OAAA7B,KAAA,QAAA2C,IAAAd,EAAA,EAAArC,EAAAsC,KAAAA,EAAA,IAAAmB,EAAA,YAAAd,IAAA,UAAAe,IAAA,UAAAC,IAAA,KAAAC,EAAA,GAAA1D,EAAA0D,EAAAhC,GAAA,8BAAAkC,EAAA7C,OAAA8C,eAAAC,EAAAF,GAAAA,EAAAA,EAAAG,EAAA,MAAAD,GAAAA,IAAAhD,GAAAG,EAAAiC,KAAAY,EAAApC,KAAAgC,EAAAI,GAAA,IAAAE,EAAAP,EAAAzC,UAAAyB,EAAAzB,UAAAD,OAAA4B,OAAAe,GAAA,SAAAO,EAAAjD,GAAA,0BAAAkD,SAAA,SAAAC,GAAAnE,EAAAgB,EAAAmD,GAAA,SAAAlB,GAAA,YAAAmB,QAAAD,EAAAlB,EAAA,gBAAAoB,EAAA3B,EAAA4B,GAAA,SAAAC,EAAAJ,EAAAlB,EAAAuB,EAAAC,GAAA,IAAAC,EAAA3B,EAAAL,EAAAyB,GAAAzB,EAAAO,GAAA,aAAAyB,EAAApE,KAAA,KAAAqE,EAAAD,EAAAzB,IAAA1B,EAAAoD,EAAApD,MAAA,OAAAA,GAAA,UAAAnB,EAAAmB,IAAAN,EAAAiC,KAAA3B,EAAA,WAAA+C,EAAAE,QAAAjD,EAAAqD,SAAAC,MAAA,SAAAtD,GAAAgD,EAAA,OAAAhD,EAAAiD,EAAAC,EAAA,aAAAtC,GAAAoC,EAAA,QAAApC,EAAAqC,EAAAC,EAAA,IAAAH,EAAAE,QAAAjD,GAAAsD,MAAA,SAAAC,GAAAH,EAAApD,MAAAuD,EAAAN,EAAAG,EAAA,aAAAI,GAAA,OAAAR,EAAA,QAAAQ,EAAAP,EAAAC,EAAA,IAAAA,EAAAC,EAAAzB,IAAA,KAAA+B,EAAA7D,EAAA,gBAAAI,MAAA,SAAA4C,EAAAlB,GAAA,SAAAgC,IAAA,WAAAX,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAlB,EAAAuB,EAAAC,EAAA,WAAAO,EAAAA,EAAAA,EAAAH,KAAAI,EAAAA,GAAAA,GAAA,aAAAnC,EAAAT,EAAAnC,EAAA0C,GAAA,IAAAsC,EAAA,iCAAAf,EAAAlB,GAAA,iBAAAiC,EAAA,UAAAC,MAAA,iDAAAD,EAAA,cAAAf,EAAA,MAAAlB,EAAA,OAAA1B,WAAAV,EAAA+E,MAAA,OAAAhD,EAAAuB,OAAAA,EAAAvB,EAAAK,IAAAA,IAAA,KAAAoC,EAAAzC,EAAAyC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAAzC,GAAA,GAAA0C,EAAA,IAAAA,IAAA/B,EAAA,gBAAA+B,CAAA,cAAA1C,EAAAuB,OAAAvB,EAAA4C,KAAA5C,EAAA6C,MAAA7C,EAAAK,SAAA,aAAAL,EAAAuB,OAAA,uBAAAe,EAAA,MAAAA,EAAA,YAAAtC,EAAAK,IAAAL,EAAA8C,kBAAA9C,EAAAK,IAAA,gBAAAL,EAAAuB,QAAAvB,EAAA+C,OAAA,SAAA/C,EAAAK,KAAAiC,EAAA,gBAAAR,EAAA3B,EAAAV,EAAAnC,EAAA0C,GAAA,cAAA8B,EAAApE,KAAA,IAAA4E,EAAAtC,EAAAgD,KAAA,6BAAAlB,EAAAzB,MAAAM,EAAA,gBAAAhC,MAAAmD,EAAAzB,IAAA2C,KAAAhD,EAAAgD,KAAA,WAAAlB,EAAApE,OAAA4E,EAAA,YAAAtC,EAAAuB,OAAA,QAAAvB,EAAAK,IAAAyB,EAAAzB,IAAA,YAAAsC,EAAAF,EAAAzC,GAAA,IAAAiD,EAAAjD,EAAAuB,OAAAA,EAAAkB,EAAA1D,SAAAkE,GAAA,QAAAhF,IAAAsD,EAAA,OAAAvB,EAAAyC,SAAA,eAAAQ,GAAAR,EAAA1D,SAAAkI,SAAAjH,EAAAuB,OAAA,SAAAvB,EAAAK,SAAApC,EAAA0E,EAAAF,EAAAzC,GAAA,UAAAA,EAAAuB,SAAA,WAAA0B,IAAAjD,EAAAuB,OAAA,QAAAvB,EAAAK,IAAA,IAAA6C,UAAA,oCAAAD,EAAA,aAAAtC,EAAA,IAAAmB,EAAA3B,EAAAoB,EAAAkB,EAAA1D,SAAAiB,EAAAK,KAAA,aAAAyB,EAAApE,KAAA,OAAAsC,EAAAuB,OAAA,QAAAvB,EAAAK,IAAAyB,EAAAzB,IAAAL,EAAAyC,SAAA,KAAA9B,EAAA,IAAAwC,EAAArB,EAAAzB,IAAA,OAAA8C,EAAAA,EAAAH,MAAAhD,EAAAyC,EAAAW,YAAAD,EAAAxE,MAAAqB,EAAAqD,KAAAZ,EAAAa,QAAA,WAAAtD,EAAAuB,SAAAvB,EAAAuB,OAAA,OAAAvB,EAAAK,SAAApC,GAAA+B,EAAAyC,SAAA,KAAA9B,GAAAwC,GAAAnD,EAAAuB,OAAA,QAAAvB,EAAAK,IAAA,IAAA6C,UAAA,oCAAAlD,EAAAyC,SAAA,KAAA9B,EAAA,UAAA4C,EAAAC,GAAA,IAAAC,EAAA,CAAAC,OAAAF,EAAA,SAAAA,IAAAC,EAAAE,SAAAH,EAAA,SAAAA,IAAAC,EAAAG,WAAAJ,EAAA,GAAAC,EAAAI,SAAAL,EAAA,SAAAM,WAAAC,KAAAN,EAAA,UAAAO,EAAAP,GAAA,IAAA3B,EAAA2B,EAAAQ,YAAA,GAAAnC,EAAApE,KAAA,gBAAAoE,EAAAzB,IAAAoD,EAAAQ,WAAAnC,CAAA,UAAA7B,EAAAN,GAAA,KAAAmE,WAAA,EAAAJ,OAAA,SAAA/D,EAAA2B,QAAAiC,EAAA,WAAAW,OAAA,YAAA/C,EAAAgD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAArF,GAAA,GAAAsF,EAAA,OAAAA,EAAA9D,KAAA6D,GAAA,sBAAAA,EAAAd,KAAA,OAAAc,EAAA,IAAAE,MAAAF,EAAAG,QAAA,KAAAC,GAAA,EAAAlB,EAAA,SAAAA,IAAA,OAAAkB,EAAAJ,EAAAG,QAAA,GAAAjG,EAAAiC,KAAA6D,EAAAI,GAAA,OAAAlB,EAAA1E,MAAAwF,EAAAI,GAAAlB,EAAAL,MAAA,EAAAK,EAAA,OAAAA,EAAA1E,WAAAV,EAAAoF,EAAAL,MAAA,EAAAK,CAAA,SAAAA,EAAAA,KAAAA,CAAA,SAAAA,KAAAb,EAAA,UAAAA,IAAA,OAAA7D,WAAAV,EAAA+E,MAAA,UAAApC,EAAAxC,UAAAyC,EAAAtC,EAAA6C,EAAA,eAAAzC,MAAAkC,EAAAxB,cAAA,IAAAd,EAAAsC,EAAA,eAAAlC,MAAAiC,EAAAvB,cAAA,IAAAuB,EAAA4D,YAAApH,EAAAyD,EAAA3B,EAAA,qBAAAhC,EAAAuH,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,GAAAA,EAAAE,YAAA,QAAAD,IAAAA,IAAA/D,GAAA,uBAAA+D,EAAAH,aAAAG,EAAAE,MAAA,EAAA3H,EAAA4H,KAAA,SAAAJ,GAAA,OAAAvG,OAAA4G,eAAA5G,OAAA4G,eAAAL,EAAA7D,IAAA6D,EAAAM,UAAAnE,EAAAzD,EAAAsH,EAAAxF,EAAA,sBAAAwF,EAAAtG,UAAAD,OAAA4B,OAAAqB,GAAAsD,CAAA,EAAAxH,EAAA+H,MAAA,SAAA5E,GAAA,OAAA2B,QAAA3B,EAAA,EAAAgB,EAAAI,EAAArD,WAAAhB,EAAAqE,EAAArD,UAAAY,GAAA,0BAAA9B,EAAAuE,cAAAA,EAAAvE,EAAAgI,MAAA,SAAAzF,EAAAC,EAAApC,EAAAqC,EAAA+B,QAAA,IAAAA,IAAAA,EAAAyD,SAAA,IAAAC,EAAA,IAAA3D,EAAAjC,EAAAC,EAAAC,EAAApC,EAAAqC,GAAA+B,GAAA,OAAAxE,EAAAuH,oBAAA/E,GAAA0F,EAAAA,EAAA/B,OAAApB,MAAA,SAAAF,GAAA,OAAAA,EAAAiB,KAAAjB,EAAApD,MAAAyG,EAAA/B,MAAA,KAAAhC,EAAAD,GAAAhE,EAAAgE,EAAAlC,EAAA,aAAA9B,EAAAgE,EAAAtC,GAAA,0BAAA1B,EAAAgE,EAAA,qDAAAlE,EAAAmI,KAAA,SAAAC,GAAA,IAAAC,EAAApH,OAAAmH,GAAAD,EAAA,WAAA5G,KAAA8G,EAAAF,EAAAtB,KAAAtF,GAAA,OAAA4G,EAAAG,UAAA,SAAAnC,IAAA,KAAAgC,EAAAf,QAAA,KAAA7F,EAAA4G,EAAAI,MAAA,GAAAhH,KAAA8G,EAAA,OAAAlC,EAAA1E,MAAAF,EAAA4E,EAAAL,MAAA,EAAAK,CAAA,QAAAA,EAAAL,MAAA,EAAAK,CAAA,GAAAnG,EAAAiE,OAAAA,EAAAlB,EAAA7B,UAAA,CAAAwG,YAAA3E,EAAAiE,MAAA,SAAAwB,GAAA,QAAAC,KAAA,OAAAtC,KAAA,OAAAT,KAAA,KAAAC,WAAA5E,EAAA,KAAA+E,MAAA,OAAAP,SAAA,UAAAlB,OAAA,YAAAlB,SAAApC,EAAA,KAAA6F,WAAAxC,QAAA0C,IAAA0B,EAAA,QAAAb,KAAA,WAAAA,EAAAe,OAAA,IAAAvH,EAAAiC,KAAA,KAAAuE,KAAAR,OAAAQ,EAAAgB,MAAA,WAAAhB,QAAA5G,EAAA,EAAA6H,KAAA,gBAAA9C,MAAA,MAAA+C,EAAA,KAAAjC,WAAA,GAAAG,WAAA,aAAA8B,EAAArI,KAAA,MAAAqI,EAAA1F,IAAA,YAAA2F,IAAA,EAAAlD,kBAAA,SAAAmD,GAAA,QAAAjD,KAAA,MAAAiD,EAAA,IAAAjG,EAAA,cAAAkG,EAAAC,EAAAC,GAAA,OAAAtE,EAAApE,KAAA,QAAAoE,EAAAzB,IAAA4F,EAAAjG,EAAAqD,KAAA8C,EAAAC,IAAApG,EAAAuB,OAAA,OAAAvB,EAAAK,SAAApC,KAAAmI,CAAA,SAAA7B,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAAzC,EAAA2B,EAAAQ,WAAA,YAAAR,EAAAC,OAAA,OAAAwC,EAAA,UAAAzC,EAAAC,QAAA,KAAAiC,KAAA,KAAAU,EAAAhI,EAAAiC,KAAAmD,EAAA,YAAA6C,EAAAjI,EAAAiC,KAAAmD,EAAA,iBAAA4C,GAAAC,EAAA,SAAAX,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,WAAAgC,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,WAAA,SAAAyC,GAAA,QAAAV,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,YAAA2C,EAAA,UAAA/D,MAAA,kDAAAoD,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,WAAA,KAAAb,OAAA,SAAArF,EAAA2C,GAAA,QAAAkE,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,QAAA,KAAAiC,MAAAtH,EAAAiC,KAAAmD,EAAA,oBAAAkC,KAAAlC,EAAAG,WAAA,KAAA2C,EAAA9C,EAAA,OAAA8C,IAAA,UAAA7I,GAAA,aAAAA,IAAA6I,EAAA7C,QAAArD,GAAAA,GAAAkG,EAAA3C,aAAA2C,EAAA,UAAAzE,EAAAyE,EAAAA,EAAAtC,WAAA,UAAAnC,EAAApE,KAAAA,EAAAoE,EAAAzB,IAAAA,EAAAkG,GAAA,KAAAhF,OAAA,YAAA8B,KAAAkD,EAAA3C,WAAAjD,GAAA,KAAA6F,SAAA1E,EAAA,EAAA0E,SAAA,SAAA1E,EAAA+B,GAAA,aAAA/B,EAAApE,KAAA,MAAAoE,EAAAzB,IAAA,gBAAAyB,EAAApE,MAAA,aAAAoE,EAAApE,KAAA,KAAA2F,KAAAvB,EAAAzB,IAAA,WAAAyB,EAAApE,MAAA,KAAAsI,KAAA,KAAA3F,IAAAyB,EAAAzB,IAAA,KAAAkB,OAAA,cAAA8B,KAAA,kBAAAvB,EAAApE,MAAAmG,IAAA,KAAAR,KAAAQ,GAAAlD,CAAA,EAAA8F,OAAA,SAAA7C,GAAA,QAAAW,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAG,aAAAA,EAAA,YAAA4C,SAAA/C,EAAAQ,WAAAR,EAAAI,UAAAG,EAAAP,GAAA9C,CAAA,GAAAuG,MAAA,SAAAxD,GAAA,QAAAa,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,SAAAA,EAAA,KAAA5B,EAAA2B,EAAAQ,WAAA,aAAAnC,EAAApE,KAAA,KAAAgJ,EAAA5E,EAAAzB,IAAA2D,EAAAP,EAAA,QAAAiD,CAAA,YAAAnE,MAAA,0BAAAoE,cAAA,SAAAxC,EAAAf,EAAAE,GAAA,YAAAb,SAAA,CAAA1D,SAAAoC,EAAAgD,GAAAf,WAAAA,EAAAE,QAAAA,GAAA,cAAA/B,SAAA,KAAAlB,SAAApC,GAAA0C,CAAA,GAAAzD,CAAA,UAAAiK,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA7I,EAAA4B,GAAA,QAAA8C,EAAAiE,EAAA3I,GAAA4B,GAAA1B,EAAAwE,EAAAxE,KAAA,OAAAwD,GAAA,YAAAN,EAAAM,EAAA,CAAAgB,EAAAH,KAAApB,EAAAjD,GAAAwG,QAAAvD,QAAAjD,GAAAsD,KAAAoF,EAAAC,EAAA,UAAAC,EAAAnH,GAAA,sBAAA9C,EAAA,KAAAkK,EAAAC,UAAA,WAAAtC,SAAA,SAAAvD,EAAAC,GAAA,IAAAuF,EAAAhH,EAAAsH,MAAApK,EAAAkK,GAAA,SAAAH,EAAA1I,GAAAwI,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA,OAAA3I,EAAA,UAAA2I,EAAA/H,GAAA4H,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA,QAAA/H,EAAA,CAAA8H,OAAApJ,EAAA,KADA,IAAM0J,EAAeC,EAAQ,KAEvBC,EAAS,eAAAC,EAAAP,EAAAP,IAAAlC,MAAG,SAAAiD,EAAOC,EAAOC,EAAOC,GAAO,IAAAC,EAAA,OAAAnB,IAAAxH,MAAA,SAAA4I,GAAA,cAAAA,EAAAzC,KAAAyC,EAAA/E,MAAA,cAAA+E,EAAA/E,KAAA,EACvBsE,EAAaM,EAAO,EAAGC,GAAQ,OAAxC,OAANC,EAAMC,EAAAxF,KAAAwF,EAAArF,OAAA,SACLoF,EAAON,UAAUG,GACrBK,QAAOd,EAAAP,IAAAlC,MAAC,SAAAwD,IAAA,OAAAtB,IAAAxH,MAAA,SAAA+I,GAAA,cAAAA,EAAA5C,KAAA4C,EAAAlF,MAAA,cAAAkF,EAAAlF,KAAA,EACD8E,EAAOK,YAAW,wBAAAD,EAAAzC,OAAA,GAAAwC,EAAA,OACxB,wBAAAF,EAAAtC,OAAA,GAAAiC,EAAA,KACL,gBANcU,EAAAC,EAAAC,GAAA,OAAAb,EAAAJ,MAAA,KAAAD,UAAA,KAQTmB,EAAM,eAAAC,EAAAtB,EAAAP,IAAAlC,MAAG,SAAAgE,EAAOd,EAAOE,GAAO,IAAAC,EAAA,OAAAnB,IAAAxH,MAAA,SAAAuJ,GAAA,cAAAA,EAAApD,KAAAoD,EAAA1F,MAAA,cAAA0F,EAAA1F,KAAA,EACbsE,EAAa,MAAO,EAAGO,GAAQ,OAAxC,OAANC,EAAMY,EAAAnG,KAAAmG,EAAAhG,OAAA,SACLoF,EAAOS,OAAOZ,GAClBK,QAAOd,EAAAP,IAAAlC,MAAC,SAAAkE,IAAA,OAAAhC,IAAAxH,MAAA,SAAAyJ,GAAA,cAAAA,EAAAtD,KAAAsD,EAAA5F,MAAA,cAAA4F,EAAA5F,KAAA,EACD8E,EAAOK,YAAW,wBAAAS,EAAAnD,OAAA,GAAAkD,EAAA,OACxB,wBAAAD,EAAAjD,OAAA,GAAAgD,EAAA,KACL,gBANWI,EAAAC,GAAA,OAAAN,EAAAnB,MAAA,KAAAD,UAAA,KAQZtK,EAAOD,QAAU,CACf2K,UAAAA,EACAe,OAAAA,YCdFzL,EAAOD,QAAU,CACfkM,eAAgB,EAChBC,UAAW,EACXC,wBAAyB,EACzBC,QAAS,YCPXpM,EAAOD,QAAU,CACfsM,SAAU,IACVC,SAAU,IACVC,UAAW,IACXC,KAAM,IACNC,cAAe,IACfC,uBAAwB,IACxBC,aAAc,IACdC,YAAa,IACbC,YAAa,IACbC,YAAa,IACbC,YAAa,KACbC,YAAa,KACbC,gBAAiB,KACjBC,SAAU,eCjBZlN,EAAOD,QAAU,CAMfoN,eAAe,EACfC,OAAQ,WAAO,UC2GjBpN,EAAOD,QAAU,CACfsN,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,SAAU,WACVC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,QAAS,UACTC,QAAS,UACTC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,QAAS,UACTC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,QAAS,UACTC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,QAAS,UACTC,IAAK,MACLC,IAAK,MACLC,SAAU,WACVC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,SAAU,WACVC,IAAK,MACLC,IAAK,sBCxNP,IAAMC,EAAQlJ,EAAQ,KAElBmJ,EAAa,EAEjB5T,EAAOD,QAAU,SAAA4K,GAIX,IAHAkJ,EAAGlJ,EAAPmJ,GACAC,EAAMpJ,EAANoJ,OAAMC,EAAArJ,EACNsJ,QAAAA,OAAO,IAAAD,EAAG,CAAC,EAACA,EAERF,EAAKD,EAMT,YALkB,IAAPC,IACTA,EAAKH,EAAM,MAAOC,GAClBA,GAAc,GAGT,CACLE,GAAAA,EACAC,OAAAA,EACAE,QAAAA,EAEJ,kSCnBApK,EAAA,kBAAA9J,CAAA,MAAAA,EAAA,GAAAgB,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAI,gBAAA,SAAAC,EAAAC,EAAAC,GAAAF,EAAAC,GAAAC,EAAAC,KAAA,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAA/B,EAAAoB,EAAAC,EAAAE,GAAA,OAAAR,OAAAI,eAAAC,EAAAC,EAAA,CAAAE,MAAAA,EAAAS,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAd,EAAAC,EAAA,KAAArB,EAAA,aAAAmC,GAAAnC,EAAA,SAAAoB,EAAAC,EAAAE,GAAA,OAAAH,EAAAC,GAAAE,CAAA,WAAAa,EAAAC,EAAAC,EAAApC,EAAAqC,GAAA,IAAAC,EAAAF,GAAAA,EAAAtB,qBAAAyB,EAAAH,EAAAG,EAAAC,EAAA3B,OAAA4B,OAAAH,EAAAxB,WAAA4B,EAAA,IAAAC,EAAAN,GAAA,WAAApB,EAAAuB,EAAA,WAAAnB,MAAAuB,EAAAT,EAAAnC,EAAA0C,KAAAF,CAAA,UAAAK,EAAAC,EAAA5B,EAAA6B,GAAA,WAAA3C,KAAA,SAAA2C,IAAAD,EAAAE,KAAA9B,EAAA6B,GAAA,OAAAd,GAAA,OAAA7B,KAAA,QAAA2C,IAAAd,EAAA,EAAArC,EAAAsC,KAAAA,EAAA,IAAAmB,EAAA,YAAAd,IAAA,UAAAe,IAAA,UAAAC,IAAA,KAAAC,EAAA,GAAA1D,EAAA0D,EAAAhC,GAAA,8BAAAkC,EAAA7C,OAAA8C,eAAAC,EAAAF,GAAAA,EAAAA,EAAAG,EAAA,MAAAD,GAAAA,IAAAhD,GAAAG,EAAAiC,KAAAY,EAAApC,KAAAgC,EAAAI,GAAA,IAAAE,EAAAP,EAAAzC,UAAAyB,EAAAzB,UAAAD,OAAA4B,OAAAe,GAAA,SAAAO,EAAAjD,GAAA,0BAAAkD,SAAA,SAAAC,GAAAnE,EAAAgB,EAAAmD,GAAA,SAAAlB,GAAA,YAAAmB,QAAAD,EAAAlB,EAAA,gBAAAoB,EAAA3B,EAAA4B,GAAA,SAAAC,EAAAJ,EAAAlB,EAAAuB,EAAAC,GAAA,IAAAC,EAAA3B,EAAAL,EAAAyB,GAAAzB,EAAAO,GAAA,aAAAyB,EAAApE,KAAA,KAAAqE,EAAAD,EAAAzB,IAAA1B,EAAAoD,EAAApD,MAAA,OAAAA,GAAA,UAAAnB,EAAAmB,IAAAN,EAAAiC,KAAA3B,EAAA,WAAA+C,EAAAE,QAAAjD,EAAAqD,SAAAC,MAAA,SAAAtD,GAAAgD,EAAA,OAAAhD,EAAAiD,EAAAC,EAAA,aAAAtC,GAAAoC,EAAA,QAAApC,EAAAqC,EAAAC,EAAA,IAAAH,EAAAE,QAAAjD,GAAAsD,MAAA,SAAAC,GAAAH,EAAApD,MAAAuD,EAAAN,EAAAG,EAAA,aAAAI,GAAA,OAAAR,EAAA,QAAAQ,EAAAP,EAAAC,EAAA,IAAAA,EAAAC,EAAAzB,IAAA,KAAA+B,EAAA7D,EAAA,gBAAAI,MAAA,SAAA4C,EAAAlB,GAAA,SAAAgC,IAAA,WAAAX,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAlB,EAAAuB,EAAAC,EAAA,WAAAO,EAAAA,EAAAA,EAAAH,KAAAI,EAAAA,GAAAA,GAAA,aAAAnC,EAAAT,EAAAnC,EAAA0C,GAAA,IAAAsC,EAAA,iCAAAf,EAAAlB,GAAA,iBAAAiC,EAAA,UAAAC,MAAA,iDAAAD,EAAA,cAAAf,EAAA,MAAAlB,EAAA,OAAA1B,WAAAV,EAAA+E,MAAA,OAAAhD,EAAAuB,OAAAA,EAAAvB,EAAAK,IAAAA,IAAA,KAAAoC,EAAAzC,EAAAyC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAAzC,GAAA,GAAA0C,EAAA,IAAAA,IAAA/B,EAAA,gBAAA+B,CAAA,cAAA1C,EAAAuB,OAAAvB,EAAA4C,KAAA5C,EAAA6C,MAAA7C,EAAAK,SAAA,aAAAL,EAAAuB,OAAA,uBAAAe,EAAA,MAAAA,EAAA,YAAAtC,EAAAK,IAAAL,EAAA8C,kBAAA9C,EAAAK,IAAA,gBAAAL,EAAAuB,QAAAvB,EAAA+C,OAAA,SAAA/C,EAAAK,KAAAiC,EAAA,gBAAAR,EAAA3B,EAAAV,EAAAnC,EAAA0C,GAAA,cAAA8B,EAAApE,KAAA,IAAA4E,EAAAtC,EAAAgD,KAAA,6BAAAlB,EAAAzB,MAAAM,EAAA,gBAAAhC,MAAAmD,EAAAzB,IAAA2C,KAAAhD,EAAAgD,KAAA,WAAAlB,EAAApE,OAAA4E,EAAA,YAAAtC,EAAAuB,OAAA,QAAAvB,EAAAK,IAAAyB,EAAAzB,IAAA,YAAAsC,EAAAF,EAAAzC,GAAA,IAAAiD,EAAAjD,EAAAuB,OAAAA,EAAAkB,EAAA1D,SAAAkE,GAAA,QAAAhF,IAAAsD,EAAA,OAAAvB,EAAAyC,SAAA,eAAAQ,GAAAR,EAAA1D,SAAAkI,SAAAjH,EAAAuB,OAAA,SAAAvB,EAAAK,SAAApC,EAAA0E,EAAAF,EAAAzC,GAAA,UAAAA,EAAAuB,SAAA,WAAA0B,IAAAjD,EAAAuB,OAAA,QAAAvB,EAAAK,IAAA,IAAA6C,UAAA,oCAAAD,EAAA,aAAAtC,EAAA,IAAAmB,EAAA3B,EAAAoB,EAAAkB,EAAA1D,SAAAiB,EAAAK,KAAA,aAAAyB,EAAApE,KAAA,OAAAsC,EAAAuB,OAAA,QAAAvB,EAAAK,IAAAyB,EAAAzB,IAAAL,EAAAyC,SAAA,KAAA9B,EAAA,IAAAwC,EAAArB,EAAAzB,IAAA,OAAA8C,EAAAA,EAAAH,MAAAhD,EAAAyC,EAAAW,YAAAD,EAAAxE,MAAAqB,EAAAqD,KAAAZ,EAAAa,QAAA,WAAAtD,EAAAuB,SAAAvB,EAAAuB,OAAA,OAAAvB,EAAAK,SAAApC,GAAA+B,EAAAyC,SAAA,KAAA9B,GAAAwC,GAAAnD,EAAAuB,OAAA,QAAAvB,EAAAK,IAAA,IAAA6C,UAAA,oCAAAlD,EAAAyC,SAAA,KAAA9B,EAAA,UAAA4C,EAAAC,GAAA,IAAAC,EAAA,CAAAC,OAAAF,EAAA,SAAAA,IAAAC,EAAAE,SAAAH,EAAA,SAAAA,IAAAC,EAAAG,WAAAJ,EAAA,GAAAC,EAAAI,SAAAL,EAAA,SAAAM,WAAAC,KAAAN,EAAA,UAAAO,EAAAP,GAAA,IAAA3B,EAAA2B,EAAAQ,YAAA,GAAAnC,EAAApE,KAAA,gBAAAoE,EAAAzB,IAAAoD,EAAAQ,WAAAnC,CAAA,UAAA7B,EAAAN,GAAA,KAAAmE,WAAA,EAAAJ,OAAA,SAAA/D,EAAA2B,QAAAiC,EAAA,WAAAW,OAAA,YAAA/C,EAAAgD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAArF,GAAA,GAAAsF,EAAA,OAAAA,EAAA9D,KAAA6D,GAAA,sBAAAA,EAAAd,KAAA,OAAAc,EAAA,IAAAE,MAAAF,EAAAG,QAAA,KAAAC,GAAA,EAAAlB,EAAA,SAAAA,IAAA,OAAAkB,EAAAJ,EAAAG,QAAA,GAAAjG,EAAAiC,KAAA6D,EAAAI,GAAA,OAAAlB,EAAA1E,MAAAwF,EAAAI,GAAAlB,EAAAL,MAAA,EAAAK,EAAA,OAAAA,EAAA1E,WAAAV,EAAAoF,EAAAL,MAAA,EAAAK,CAAA,SAAAA,EAAAA,KAAAA,CAAA,SAAAA,KAAAb,EAAA,UAAAA,IAAA,OAAA7D,WAAAV,EAAA+E,MAAA,UAAApC,EAAAxC,UAAAyC,EAAAtC,EAAA6C,EAAA,eAAAzC,MAAAkC,EAAAxB,cAAA,IAAAd,EAAAsC,EAAA,eAAAlC,MAAAiC,EAAAvB,cAAA,IAAAuB,EAAA4D,YAAApH,EAAAyD,EAAA3B,EAAA,qBAAAhC,EAAAuH,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,GAAAA,EAAAE,YAAA,QAAAD,IAAAA,IAAA/D,GAAA,uBAAA+D,EAAAH,aAAAG,EAAAE,MAAA,EAAA3H,EAAA4H,KAAA,SAAAJ,GAAA,OAAAvG,OAAA4G,eAAA5G,OAAA4G,eAAAL,EAAA7D,IAAA6D,EAAAM,UAAAnE,EAAAzD,EAAAsH,EAAAxF,EAAA,sBAAAwF,EAAAtG,UAAAD,OAAA4B,OAAAqB,GAAAsD,CAAA,EAAAxH,EAAA+H,MAAA,SAAA5E,GAAA,OAAA2B,QAAA3B,EAAA,EAAAgB,EAAAI,EAAArD,WAAAhB,EAAAqE,EAAArD,UAAAY,GAAA,0BAAA9B,EAAAuE,cAAAA,EAAAvE,EAAAgI,MAAA,SAAAzF,EAAAC,EAAApC,EAAAqC,EAAA+B,QAAA,IAAAA,IAAAA,EAAAyD,SAAA,IAAAC,EAAA,IAAA3D,EAAAjC,EAAAC,EAAAC,EAAApC,EAAAqC,GAAA+B,GAAA,OAAAxE,EAAAuH,oBAAA/E,GAAA0F,EAAAA,EAAA/B,OAAApB,MAAA,SAAAF,GAAA,OAAAA,EAAAiB,KAAAjB,EAAApD,MAAAyG,EAAA/B,MAAA,KAAAhC,EAAAD,GAAAhE,EAAAgE,EAAAlC,EAAA,aAAA9B,EAAAgE,EAAAtC,GAAA,0BAAA1B,EAAAgE,EAAA,qDAAAlE,EAAAmI,KAAA,SAAAC,GAAA,IAAAC,EAAApH,OAAAmH,GAAAD,EAAA,WAAA5G,KAAA8G,EAAAF,EAAAtB,KAAAtF,GAAA,OAAA4G,EAAAG,UAAA,SAAAnC,IAAA,KAAAgC,EAAAf,QAAA,KAAA7F,EAAA4G,EAAAI,MAAA,GAAAhH,KAAA8G,EAAA,OAAAlC,EAAA1E,MAAAF,EAAA4E,EAAAL,MAAA,EAAAK,CAAA,QAAAA,EAAAL,MAAA,EAAAK,CAAA,GAAAnG,EAAAiE,OAAAA,EAAAlB,EAAA7B,UAAA,CAAAwG,YAAA3E,EAAAiE,MAAA,SAAAwB,GAAA,QAAAC,KAAA,OAAAtC,KAAA,OAAAT,KAAA,KAAAC,WAAA5E,EAAA,KAAA+E,MAAA,OAAAP,SAAA,UAAAlB,OAAA,YAAAlB,SAAApC,EAAA,KAAA6F,WAAAxC,QAAA0C,IAAA0B,EAAA,QAAAb,KAAA,WAAAA,EAAAe,OAAA,IAAAvH,EAAAiC,KAAA,KAAAuE,KAAAR,OAAAQ,EAAAgB,MAAA,WAAAhB,QAAA5G,EAAA,EAAA6H,KAAA,gBAAA9C,MAAA,MAAA+C,EAAA,KAAAjC,WAAA,GAAAG,WAAA,aAAA8B,EAAArI,KAAA,MAAAqI,EAAA1F,IAAA,YAAA2F,IAAA,EAAAlD,kBAAA,SAAAmD,GAAA,QAAAjD,KAAA,MAAAiD,EAAA,IAAAjG,EAAA,cAAAkG,EAAAC,EAAAC,GAAA,OAAAtE,EAAApE,KAAA,QAAAoE,EAAAzB,IAAA4F,EAAAjG,EAAAqD,KAAA8C,EAAAC,IAAApG,EAAAuB,OAAA,OAAAvB,EAAAK,SAAApC,KAAAmI,CAAA,SAAA7B,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAAzC,EAAA2B,EAAAQ,WAAA,YAAAR,EAAAC,OAAA,OAAAwC,EAAA,UAAAzC,EAAAC,QAAA,KAAAiC,KAAA,KAAAU,EAAAhI,EAAAiC,KAAAmD,EAAA,YAAA6C,EAAAjI,EAAAiC,KAAAmD,EAAA,iBAAA4C,GAAAC,EAAA,SAAAX,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,WAAAgC,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,WAAA,SAAAyC,GAAA,QAAAV,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,YAAA2C,EAAA,UAAA/D,MAAA,kDAAAoD,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,WAAA,KAAAb,OAAA,SAAArF,EAAA2C,GAAA,QAAAkE,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,QAAA,KAAAiC,MAAAtH,EAAAiC,KAAAmD,EAAA,oBAAAkC,KAAAlC,EAAAG,WAAA,KAAA2C,EAAA9C,EAAA,OAAA8C,IAAA,UAAA7I,GAAA,aAAAA,IAAA6I,EAAA7C,QAAArD,GAAAA,GAAAkG,EAAA3C,aAAA2C,EAAA,UAAAzE,EAAAyE,EAAAA,EAAAtC,WAAA,UAAAnC,EAAApE,KAAAA,EAAAoE,EAAAzB,IAAAA,EAAAkG,GAAA,KAAAhF,OAAA,YAAA8B,KAAAkD,EAAA3C,WAAAjD,GAAA,KAAA6F,SAAA1E,EAAA,EAAA0E,SAAA,SAAA1E,EAAA+B,GAAA,aAAA/B,EAAApE,KAAA,MAAAoE,EAAAzB,IAAA,gBAAAyB,EAAApE,MAAA,aAAAoE,EAAApE,KAAA,KAAA2F,KAAAvB,EAAAzB,IAAA,WAAAyB,EAAApE,MAAA,KAAAsI,KAAA,KAAA3F,IAAAyB,EAAAzB,IAAA,KAAAkB,OAAA,cAAA8B,KAAA,kBAAAvB,EAAApE,MAAAmG,IAAA,KAAAR,KAAAQ,GAAAlD,CAAA,EAAA8F,OAAA,SAAA7C,GAAA,QAAAW,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAG,aAAAA,EAAA,YAAA4C,SAAA/C,EAAAQ,WAAAR,EAAAI,UAAAG,EAAAP,GAAA9C,CAAA,GAAAuG,MAAA,SAAAxD,GAAA,QAAAa,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,SAAAA,EAAA,KAAA5B,EAAA2B,EAAAQ,WAAA,aAAAnC,EAAApE,KAAA,KAAAgJ,EAAA5E,EAAAzB,IAAA2D,EAAAP,EAAA,QAAAiD,CAAA,YAAAnE,MAAA,0BAAAoE,cAAA,SAAAxC,EAAAf,EAAAE,GAAA,YAAAb,SAAA,CAAA1D,SAAAoC,EAAAgD,GAAAf,WAAAA,EAAAE,QAAAA,GAAA,cAAA/B,SAAA,KAAAlB,SAAApC,GAAA0C,CAAA,GAAAzD,CAAA,UAAAmU,EAAAC,EAAAC,IAAA,MAAAA,GAAAA,EAAAD,EAAAhN,UAAAiN,EAAAD,EAAAhN,QAAA,QAAAC,EAAA,EAAAiN,EAAA,IAAAC,MAAAF,GAAAhN,EAAAgN,EAAAhN,IAAAiN,EAAAjN,GAAA+M,EAAA/M,GAAA,OAAAiN,CAAA,UAAArK,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA7I,EAAA4B,GAAA,QAAA8C,EAAAiE,EAAA3I,GAAA4B,GAAA1B,EAAAwE,EAAAxE,KAAA,OAAAwD,GAAA,YAAAN,EAAAM,EAAA,CAAAgB,EAAAH,KAAApB,EAAAjD,GAAAwG,QAAAvD,QAAAjD,GAAAsD,KAAAoF,EAAAC,EAAA,UAAAC,EAAAnH,GAAA,sBAAA9C,EAAA,KAAAkK,EAAAC,UAAA,WAAAtC,SAAA,SAAAvD,EAAAC,GAAA,IAAAuF,EAAAhH,EAAAsH,MAAApK,EAAAkK,GAAA,SAAAH,EAAA1I,GAAAwI,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA,OAAA3I,EAAA,UAAA2I,EAAA/H,GAAA4H,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA,QAAA/H,EAAA,CAAA8H,OAAApJ,EAAA,KADA,IAAMyT,EAAY9J,EAAQ,KAClB+J,EAAQ/J,EAAQ,KAAhB+J,IACFb,EAAQlJ,EAAQ,KAElBgK,EAAmB,EAEvBzU,EAAOD,QAAU,WACf,IAAM+T,EAAKH,EAAM,YAAac,GACxBC,EAAU,CAAC,EACXC,EAAiB,CAAC,EACpBC,EAAW,GAEfH,GAAoB,EAEpB,IACMI,EAAgB,WAAH,OAAS7T,OAAOkH,KAAKwM,GAASvN,MAAM,EAEjD2N,EAAU,WACd,GAAwB,IAApBF,EAASzN,OAEX,IADA,IAAM4N,EAAO/T,OAAOkH,KAAKwM,GAChBtN,EAAI,EAAGA,EAAI2N,EAAK5N,OAAQC,GAAK,EACpC,QAAuC,IAA5BuN,EAAeI,EAAK3N,IAAqB,CAClDwN,EAAS,GAAGF,EAAQK,EAAK3N,KACzB,KACF,CAGN,EAEM4N,EAAQ,SAACjB,EAAQE,GAAO,OAC5B,IAAIjM,SAAQ,SAACvD,EAASC,GACpB,IAAMuQ,EAAMV,EAAU,CAAER,OAAAA,EAAQE,QAAAA,IAChCW,EAAShO,KAAI,eAAA+D,EAAAP,EAAAP,IAAAlC,MAAC,SAAAwD,EAAO+J,GAAC,OAAArL,IAAAxH,MAAA,SAAA+I,GAAA,cAAAA,EAAA5C,KAAA4C,EAAAlF,MAAA,OAIX,OAHT0O,EAASO,QACTR,EAAeO,EAAEpB,IAAMmB,EAAI7J,EAAA5C,KAAA,EAAA4C,EAAAgK,GAEzB3Q,EAAO2G,EAAAlF,KAAA,EAAOgP,EAAEnB,GAAQxJ,MAAM8K,EAAM,GAAFC,OAnC5C,SAAAnB,GAAA,GAAAG,MAAAiB,QAAApB,GAAA,OAAAD,EAAAC,EAAA,CAAAqB,CAAArB,EAmCkDF,IAnClD,SAAAhM,GAAA,uBAAAvG,QAAA,MAAAuG,EAAAvG,OAAAE,WAAA,MAAAqG,EAAA,qBAAAqM,MAAAmB,KAAAxN,EAAA,CAAAyN,CAAAvB,IAAA,SAAAwB,EAAAC,GAAA,GAAAD,EAAA,qBAAAA,EAAA,OAAAzB,EAAAyB,EAAAC,GAAA,IAAAC,EAAA7U,OAAAC,UAAA6U,SAAA3S,KAAAwS,GAAAjN,MAAA,uBAAAmN,GAAAF,EAAAlO,cAAAoO,EAAAF,EAAAlO,YAAAC,MAAA,QAAAmO,GAAA,QAAAA,EAAAvB,MAAAmB,KAAAE,GAAA,cAAAE,GAAA,2CAAAE,KAAAF,GAAA3B,EAAAyB,EAAAC,QAAA,GAAAI,CAAA7B,IAAA,qBAAApO,UAAA,wIAAAkQ,GAmCyD,CAAEhB,EAAInB,MAAI,OAAA1I,EAAA8K,GAAA9K,EAAA3F,MAAA,EAAA2F,EAAAgK,IAAAhK,EAAA8K,IAAA9K,EAAAlF,KAAA,iBAAAkF,EAAA5C,KAAA,GAAA4C,EAAA+K,GAAA/K,EAAA,SAEzD1G,EAAM0G,EAAA+K,IAAM,QAGF,OAHE/K,EAAA5C,KAAA,UAELmM,EAAeO,EAAEpB,IACxBgB,IAAU1J,EAAA9B,OAAA,6BAAA8B,EAAAzC,OAxCpB,IAAAwL,CAwCoB,GAAAhJ,EAAA,yBAEb,gBAAAG,GAAA,OAAAX,EAAAJ,MAAA,KAAAD,UAAA,EAXY,IAYbkK,EAAI,IAADc,OAAKxB,EAAE,WAAAwB,OAAUL,EAAInB,GAAE,iBAC1BU,EAAI,IAADc,OAAKxB,EAAE,uBAAAwB,OAAsBV,EAASzN,SACzC2N,GACF,GAAE,EAWEsB,EAAM,eAAAC,EAAAjM,EAAAP,IAAAlC,MAAG,SAAAiD,EAAOmJ,GAAM,IAAAuC,EAAArC,EAAAsC,EAAAC,EAAAlM,UAAA,OAAAT,IAAAxH,MAAA,SAAA4I,GAAA,cAAAA,EAAAzC,KAAAyC,EAAA/E,MAAA,UACF,IAApB2O,IAAqB,CAAA5J,EAAA/E,KAAA,cACjBd,MAAM,IAADkQ,OAAKxB,EAAE,+DAA6D,WAAAwC,EAAAE,EAAArP,OAFlD8M,EAAO,IAAAK,MAAAgC,EAAA,EAAAA,EAAA,KAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAPtC,EAAOsC,EAAA,GAAAC,EAAAD,GAAA,OAAAtL,EAAArF,OAAA,SAI/BoP,EAAMjB,EAAQE,IAAQ,wBAAAhJ,EAAAtC,OAAA,GAAAiC,EAAA,KAC9B,gBALWW,GAAA,OAAA8K,EAAA9L,MAAA,KAAAD,UAAA,KAONe,EAAS,eAAAK,EAAAtB,EAAAP,IAAAlC,MAAG,SAAAgE,IAAA,OAAA9B,IAAAxH,MAAA,SAAAuJ,GAAA,cAAAA,EAAApD,KAAAoD,EAAA1F,MAAA,OAChBlF,OAAOkH,KAAKwM,GAASvQ,QAAO,eAAAsS,EAAArM,EAAAP,IAAAlC,MAAC,SAAAkE,EAAO6K,GAAG,OAAA7M,IAAAxH,MAAA,SAAAyJ,GAAA,cAAAA,EAAAtD,KAAAsD,EAAA5F,MAAA,cAAA4F,EAAA5F,KAAA,EAC/BwO,EAAQgC,GAAKrL,YAAW,wBAAAS,EAAAnD,OAAA,GAAAkD,EAAA,KAC/B,gBAAAL,GAAA,OAAAiL,EAAAlM,MAAA,KAAAD,UAAA,EAF2B,IAG5BsK,EAAW,GAAG,wBAAAhJ,EAAAjD,OAAA,GAAAgD,EAAA,KACf,kBALc,OAAAD,EAAAnB,MAAA,KAAAD,UAAA,KAOf,MAAO,CACLqM,UAvBgB,SAACzB,GAKjB,OAJAR,EAAQQ,EAAEpB,IAAMoB,EAChBV,EAAI,IAADc,OAAKxB,EAAE,WAAAwB,OAAUJ,EAAEpB,KACtBU,EAAI,IAADc,OAAKxB,EAAE,yBAAAwB,OAAwBT,MAClCC,IACOI,EAAEpB,EACX,EAkBEsC,OAAAA,EACA/K,UAAAA,EACAuL,YA9DkB,WAAH,OAAShC,EAASzN,MAAM,EA+DvC0N,cAAAA,EAEJ,iTC9EAhL,EAAA,kBAAA9J,CAAA,MAAAA,EAAA,GAAAgB,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAI,gBAAA,SAAAC,EAAAC,EAAAC,GAAAF,EAAAC,GAAAC,EAAAC,KAAA,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAA/B,EAAAoB,EAAAC,EAAAE,GAAA,OAAAR,OAAAI,eAAAC,EAAAC,EAAA,CAAAE,MAAAA,EAAAS,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAd,EAAAC,EAAA,KAAArB,EAAA,aAAAmC,GAAAnC,EAAA,SAAAoB,EAAAC,EAAAE,GAAA,OAAAH,EAAAC,GAAAE,CAAA,WAAAa,EAAAC,EAAAC,EAAApC,EAAAqC,GAAA,IAAAC,EAAAF,GAAAA,EAAAtB,qBAAAyB,EAAAH,EAAAG,EAAAC,EAAA3B,OAAA4B,OAAAH,EAAAxB,WAAA4B,EAAA,IAAAC,EAAAN,GAAA,WAAApB,EAAAuB,EAAA,WAAAnB,MAAAuB,EAAAT,EAAAnC,EAAA0C,KAAAF,CAAA,UAAAK,EAAAC,EAAA5B,EAAA6B,GAAA,WAAA3C,KAAA,SAAA2C,IAAAD,EAAAE,KAAA9B,EAAA6B,GAAA,OAAAd,GAAA,OAAA7B,KAAA,QAAA2C,IAAAd,EAAA,EAAArC,EAAAsC,KAAAA,EAAA,IAAAmB,EAAA,YAAAd,IAAA,UAAAe,IAAA,UAAAC,IAAA,KAAAC,EAAA,GAAA1D,EAAA0D,EAAAhC,GAAA,8BAAAkC,EAAA7C,OAAA8C,eAAAC,EAAAF,GAAAA,EAAAA,EAAAG,EAAA,MAAAD,GAAAA,IAAAhD,GAAAG,EAAAiC,KAAAY,EAAApC,KAAAgC,EAAAI,GAAA,IAAAE,EAAAP,EAAAzC,UAAAyB,EAAAzB,UAAAD,OAAA4B,OAAAe,GAAA,SAAAO,EAAAjD,GAAA,0BAAAkD,SAAA,SAAAC,GAAAnE,EAAAgB,EAAAmD,GAAA,SAAAlB,GAAA,YAAAmB,QAAAD,EAAAlB,EAAA,gBAAAoB,EAAA3B,EAAA4B,GAAA,SAAAC,EAAAJ,EAAAlB,EAAAuB,EAAAC,GAAA,IAAAC,EAAA3B,EAAAL,EAAAyB,GAAAzB,EAAAO,GAAA,aAAAyB,EAAApE,KAAA,KAAAqE,EAAAD,EAAAzB,IAAA1B,EAAAoD,EAAApD,MAAA,OAAAA,GAAA,UAAAnB,EAAAmB,IAAAN,EAAAiC,KAAA3B,EAAA,WAAA+C,EAAAE,QAAAjD,EAAAqD,SAAAC,MAAA,SAAAtD,GAAAgD,EAAA,OAAAhD,EAAAiD,EAAAC,EAAA,aAAAtC,GAAAoC,EAAA,QAAApC,EAAAqC,EAAAC,EAAA,IAAAH,EAAAE,QAAAjD,GAAAsD,MAAA,SAAAC,GAAAH,EAAApD,MAAAuD,EAAAN,EAAAG,EAAA,aAAAI,GAAA,OAAAR,EAAA,QAAAQ,EAAAP,EAAAC,EAAA,IAAAA,EAAAC,EAAAzB,IAAA,KAAA+B,EAAA7D,EAAA,gBAAAI,MAAA,SAAA4C,EAAAlB,GAAA,SAAAgC,IAAA,WAAAX,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAlB,EAAAuB,EAAAC,EAAA,WAAAO,EAAAA,EAAAA,EAAAH,KAAAI,EAAAA,GAAAA,GAAA,aAAAnC,EAAAT,EAAAnC,EAAA0C,GAAA,IAAAsC,EAAA,iCAAAf,EAAAlB,GAAA,iBAAAiC,EAAA,UAAAC,MAAA,iDAAAD,EAAA,cAAAf,EAAA,MAAAlB,EAAA,OAAA1B,WAAAV,EAAA+E,MAAA,OAAAhD,EAAAuB,OAAAA,EAAAvB,EAAAK,IAAAA,IAAA,KAAAoC,EAAAzC,EAAAyC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAAzC,GAAA,GAAA0C,EAAA,IAAAA,IAAA/B,EAAA,gBAAA+B,CAAA,cAAA1C,EAAAuB,OAAAvB,EAAA4C,KAAA5C,EAAA6C,MAAA7C,EAAAK,SAAA,aAAAL,EAAAuB,OAAA,uBAAAe,EAAA,MAAAA,EAAA,YAAAtC,EAAAK,IAAAL,EAAA8C,kBAAA9C,EAAAK,IAAA,gBAAAL,EAAAuB,QAAAvB,EAAA+C,OAAA,SAAA/C,EAAAK,KAAAiC,EAAA,gBAAAR,EAAA3B,EAAAV,EAAAnC,EAAA0C,GAAA,cAAA8B,EAAApE,KAAA,IAAA4E,EAAAtC,EAAAgD,KAAA,6BAAAlB,EAAAzB,MAAAM,EAAA,gBAAAhC,MAAAmD,EAAAzB,IAAA2C,KAAAhD,EAAAgD,KAAA,WAAAlB,EAAApE,OAAA4E,EAAA,YAAAtC,EAAAuB,OAAA,QAAAvB,EAAAK,IAAAyB,EAAAzB,IAAA,YAAAsC,EAAAF,EAAAzC,GAAA,IAAAiD,EAAAjD,EAAAuB,OAAAA,EAAAkB,EAAA1D,SAAAkE,GAAA,QAAAhF,IAAAsD,EAAA,OAAAvB,EAAAyC,SAAA,eAAAQ,GAAAR,EAAA1D,SAAAkI,SAAAjH,EAAAuB,OAAA,SAAAvB,EAAAK,SAAApC,EAAA0E,EAAAF,EAAAzC,GAAA,UAAAA,EAAAuB,SAAA,WAAA0B,IAAAjD,EAAAuB,OAAA,QAAAvB,EAAAK,IAAA,IAAA6C,UAAA,oCAAAD,EAAA,aAAAtC,EAAA,IAAAmB,EAAA3B,EAAAoB,EAAAkB,EAAA1D,SAAAiB,EAAAK,KAAA,aAAAyB,EAAApE,KAAA,OAAAsC,EAAAuB,OAAA,QAAAvB,EAAAK,IAAAyB,EAAAzB,IAAAL,EAAAyC,SAAA,KAAA9B,EAAA,IAAAwC,EAAArB,EAAAzB,IAAA,OAAA8C,EAAAA,EAAAH,MAAAhD,EAAAyC,EAAAW,YAAAD,EAAAxE,MAAAqB,EAAAqD,KAAAZ,EAAAa,QAAA,WAAAtD,EAAAuB,SAAAvB,EAAAuB,OAAA,OAAAvB,EAAAK,SAAApC,GAAA+B,EAAAyC,SAAA,KAAA9B,GAAAwC,GAAAnD,EAAAuB,OAAA,QAAAvB,EAAAK,IAAA,IAAA6C,UAAA,oCAAAlD,EAAAyC,SAAA,KAAA9B,EAAA,UAAA4C,EAAAC,GAAA,IAAAC,EAAA,CAAAC,OAAAF,EAAA,SAAAA,IAAAC,EAAAE,SAAAH,EAAA,SAAAA,IAAAC,EAAAG,WAAAJ,EAAA,GAAAC,EAAAI,SAAAL,EAAA,SAAAM,WAAAC,KAAAN,EAAA,UAAAO,EAAAP,GAAA,IAAA3B,EAAA2B,EAAAQ,YAAA,GAAAnC,EAAApE,KAAA,gBAAAoE,EAAAzB,IAAAoD,EAAAQ,WAAAnC,CAAA,UAAA7B,EAAAN,GAAA,KAAAmE,WAAA,EAAAJ,OAAA,SAAA/D,EAAA2B,QAAAiC,EAAA,WAAAW,OAAA,YAAA/C,EAAAgD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAArF,GAAA,GAAAsF,EAAA,OAAAA,EAAA9D,KAAA6D,GAAA,sBAAAA,EAAAd,KAAA,OAAAc,EAAA,IAAAE,MAAAF,EAAAG,QAAA,KAAAC,GAAA,EAAAlB,EAAA,SAAAA,IAAA,OAAAkB,EAAAJ,EAAAG,QAAA,GAAAjG,EAAAiC,KAAA6D,EAAAI,GAAA,OAAAlB,EAAA1E,MAAAwF,EAAAI,GAAAlB,EAAAL,MAAA,EAAAK,EAAA,OAAAA,EAAA1E,WAAAV,EAAAoF,EAAAL,MAAA,EAAAK,CAAA,SAAAA,EAAAA,KAAAA,CAAA,SAAAA,KAAAb,EAAA,UAAAA,IAAA,OAAA7D,WAAAV,EAAA+E,MAAA,UAAApC,EAAAxC,UAAAyC,EAAAtC,EAAA6C,EAAA,eAAAzC,MAAAkC,EAAAxB,cAAA,IAAAd,EAAAsC,EAAA,eAAAlC,MAAAiC,EAAAvB,cAAA,IAAAuB,EAAA4D,YAAApH,EAAAyD,EAAA3B,EAAA,qBAAAhC,EAAAuH,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,GAAAA,EAAAE,YAAA,QAAAD,IAAAA,IAAA/D,GAAA,uBAAA+D,EAAAH,aAAAG,EAAAE,MAAA,EAAA3H,EAAA4H,KAAA,SAAAJ,GAAA,OAAAvG,OAAA4G,eAAA5G,OAAA4G,eAAAL,EAAA7D,IAAA6D,EAAAM,UAAAnE,EAAAzD,EAAAsH,EAAAxF,EAAA,sBAAAwF,EAAAtG,UAAAD,OAAA4B,OAAAqB,GAAAsD,CAAA,EAAAxH,EAAA+H,MAAA,SAAA5E,GAAA,OAAA2B,QAAA3B,EAAA,EAAAgB,EAAAI,EAAArD,WAAAhB,EAAAqE,EAAArD,UAAAY,GAAA,0BAAA9B,EAAAuE,cAAAA,EAAAvE,EAAAgI,MAAA,SAAAzF,EAAAC,EAAApC,EAAAqC,EAAA+B,QAAA,IAAAA,IAAAA,EAAAyD,SAAA,IAAAC,EAAA,IAAA3D,EAAAjC,EAAAC,EAAAC,EAAApC,EAAAqC,GAAA+B,GAAA,OAAAxE,EAAAuH,oBAAA/E,GAAA0F,EAAAA,EAAA/B,OAAApB,MAAA,SAAAF,GAAA,OAAAA,EAAAiB,KAAAjB,EAAApD,MAAAyG,EAAA/B,MAAA,KAAAhC,EAAAD,GAAAhE,EAAAgE,EAAAlC,EAAA,aAAA9B,EAAAgE,EAAAtC,GAAA,0BAAA1B,EAAAgE,EAAA,qDAAAlE,EAAAmI,KAAA,SAAAC,GAAA,IAAAC,EAAApH,OAAAmH,GAAAD,EAAA,WAAA5G,KAAA8G,EAAAF,EAAAtB,KAAAtF,GAAA,OAAA4G,EAAAG,UAAA,SAAAnC,IAAA,KAAAgC,EAAAf,QAAA,KAAA7F,EAAA4G,EAAAI,MAAA,GAAAhH,KAAA8G,EAAA,OAAAlC,EAAA1E,MAAAF,EAAA4E,EAAAL,MAAA,EAAAK,CAAA,QAAAA,EAAAL,MAAA,EAAAK,CAAA,GAAAnG,EAAAiE,OAAAA,EAAAlB,EAAA7B,UAAA,CAAAwG,YAAA3E,EAAAiE,MAAA,SAAAwB,GAAA,QAAAC,KAAA,OAAAtC,KAAA,OAAAT,KAAA,KAAAC,WAAA5E,EAAA,KAAA+E,MAAA,OAAAP,SAAA,UAAAlB,OAAA,YAAAlB,SAAApC,EAAA,KAAA6F,WAAAxC,QAAA0C,IAAA0B,EAAA,QAAAb,KAAA,WAAAA,EAAAe,OAAA,IAAAvH,EAAAiC,KAAA,KAAAuE,KAAAR,OAAAQ,EAAAgB,MAAA,WAAAhB,QAAA5G,EAAA,EAAA6H,KAAA,gBAAA9C,MAAA,MAAA+C,EAAA,KAAAjC,WAAA,GAAAG,WAAA,aAAA8B,EAAArI,KAAA,MAAAqI,EAAA1F,IAAA,YAAA2F,IAAA,EAAAlD,kBAAA,SAAAmD,GAAA,QAAAjD,KAAA,MAAAiD,EAAA,IAAAjG,EAAA,cAAAkG,EAAAC,EAAAC,GAAA,OAAAtE,EAAApE,KAAA,QAAAoE,EAAAzB,IAAA4F,EAAAjG,EAAAqD,KAAA8C,EAAAC,IAAApG,EAAAuB,OAAA,OAAAvB,EAAAK,SAAApC,KAAAmI,CAAA,SAAA7B,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAAzC,EAAA2B,EAAAQ,WAAA,YAAAR,EAAAC,OAAA,OAAAwC,EAAA,UAAAzC,EAAAC,QAAA,KAAAiC,KAAA,KAAAU,EAAAhI,EAAAiC,KAAAmD,EAAA,YAAA6C,EAAAjI,EAAAiC,KAAAmD,EAAA,iBAAA4C,GAAAC,EAAA,SAAAX,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,WAAAgC,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,WAAA,SAAAyC,GAAA,QAAAV,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,YAAA2C,EAAA,UAAA/D,MAAA,kDAAAoD,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,WAAA,KAAAb,OAAA,SAAArF,EAAA2C,GAAA,QAAAkE,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,QAAA,KAAAiC,MAAAtH,EAAAiC,KAAAmD,EAAA,oBAAAkC,KAAAlC,EAAAG,WAAA,KAAA2C,EAAA9C,EAAA,OAAA8C,IAAA,UAAA7I,GAAA,aAAAA,IAAA6I,EAAA7C,QAAArD,GAAAA,GAAAkG,EAAA3C,aAAA2C,EAAA,UAAAzE,EAAAyE,EAAAA,EAAAtC,WAAA,UAAAnC,EAAApE,KAAAA,EAAAoE,EAAAzB,IAAAA,EAAAkG,GAAA,KAAAhF,OAAA,YAAA8B,KAAAkD,EAAA3C,WAAAjD,GAAA,KAAA6F,SAAA1E,EAAA,EAAA0E,SAAA,SAAA1E,EAAA+B,GAAA,aAAA/B,EAAApE,KAAA,MAAAoE,EAAAzB,IAAA,gBAAAyB,EAAApE,MAAA,aAAAoE,EAAApE,KAAA,KAAA2F,KAAAvB,EAAAzB,IAAA,WAAAyB,EAAApE,MAAA,KAAAsI,KAAA,KAAA3F,IAAAyB,EAAAzB,IAAA,KAAAkB,OAAA,cAAA8B,KAAA,kBAAAvB,EAAApE,MAAAmG,IAAA,KAAAR,KAAAQ,GAAAlD,CAAA,EAAA8F,OAAA,SAAA7C,GAAA,QAAAW,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAG,aAAAA,EAAA,YAAA4C,SAAA/C,EAAAQ,WAAAR,EAAAI,UAAAG,EAAAP,GAAA9C,CAAA,GAAAuG,MAAA,SAAAxD,GAAA,QAAAa,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,SAAAA,EAAA,KAAA5B,EAAA2B,EAAAQ,WAAA,aAAAnC,EAAApE,KAAA,KAAAgJ,EAAA5E,EAAAzB,IAAA2D,EAAAP,EAAA,QAAAiD,CAAA,YAAAnE,MAAA,0BAAAoE,cAAA,SAAAxC,EAAAf,EAAAE,GAAA,YAAAb,SAAA,CAAA1D,SAAAoC,EAAAgD,GAAAf,WAAAA,EAAAE,QAAAA,GAAA,cAAA/B,SAAA,KAAAlB,SAAApC,GAAA0C,CAAA,GAAAzD,CAAA,UAAAmU,EAAAC,EAAAC,IAAA,MAAAA,GAAAA,EAAAD,EAAAhN,UAAAiN,EAAAD,EAAAhN,QAAA,QAAAC,EAAA,EAAAiN,EAAA,IAAAC,MAAAF,GAAAhN,EAAAgN,EAAAhN,IAAAiN,EAAAjN,GAAA+M,EAAA/M,GAAA,OAAAiN,CAAA,UAAAwC,EAAAzO,EAAA0O,GAAA,IAAA5O,EAAAlH,OAAAkH,KAAAE,GAAA,GAAApH,OAAA+V,sBAAA,KAAAC,EAAAhW,OAAA+V,sBAAA3O,GAAA0O,IAAAE,EAAAA,EAAAC,QAAA,SAAAC,GAAA,OAAAlW,OAAAmW,yBAAA/O,EAAA8O,GAAAjV,UAAA,KAAAiG,EAAAtB,KAAA2D,MAAArC,EAAA8O,EAAA,QAAA9O,CAAA,UAAAkP,EAAAC,GAAA,QAAAjQ,EAAA,EAAAA,EAAAkD,UAAAnD,OAAAC,IAAA,KAAAkQ,EAAA,MAAAhN,UAAAlD,GAAAkD,UAAAlD,GAAA,GAAAA,EAAA,EAAAyP,EAAA7V,OAAAsW,IAAA,GAAAnT,SAAA,SAAA7C,GAAAiW,EAAAF,EAAA/V,EAAAgW,EAAAhW,GAAA,IAAAN,OAAAwW,0BAAAxW,OAAAyW,iBAAAJ,EAAArW,OAAAwW,0BAAAF,IAAAT,EAAA7V,OAAAsW,IAAAnT,SAAA,SAAA7C,GAAAN,OAAAI,eAAAiW,EAAA/V,EAAAN,OAAAmW,yBAAAG,EAAAhW,GAAA,WAAA+V,CAAA,UAAAE,EAAAlW,EAAAC,EAAAE,GAAA,OAAAF,EAAA,SAAA4B,GAAA,IAAA5B,EAAA,SAAAoW,EAAAC,GAAA,cAAAtX,EAAAqX,IAAA,OAAAA,EAAA,OAAAA,EAAA,IAAAE,EAAAF,EAAAhW,OAAAmW,aAAA,QAAA/W,IAAA8W,EAAA,KAAAE,EAAAF,EAAAzU,KAAAuU,EAAAC,UAAA,cAAAtX,EAAAyX,GAAA,OAAAA,EAAA,UAAA/R,UAAA,uDAAAgS,OAAAL,EAAA,CAAAM,CAAA9U,GAAA,iBAAA7C,EAAAiB,GAAAA,EAAAyW,OAAAzW,EAAA,CAAA2W,CAAA3W,MAAAD,EAAAL,OAAAI,eAAAC,EAAAC,EAAA,CAAAE,MAAAA,EAAAS,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAd,EAAAC,GAAAE,EAAAH,CAAA,UAAA6W,EAAAZ,EAAAa,GAAA,SAAAb,EAAA,aAAAhW,EAAA8F,EAAAiQ,EAAA,SAAAC,EAAAa,GAAA,SAAAb,EAAA,aAAAhW,EAAA8F,EAAAiQ,EAAA,GAAAe,EAAApX,OAAAkH,KAAAoP,GAAA,IAAAlQ,EAAA,EAAAA,EAAAgR,EAAAjR,OAAAC,IAAA9F,EAAA8W,EAAAhR,GAAA+Q,EAAAvX,QAAAU,IAAA,IAAA+V,EAAA/V,GAAAgW,EAAAhW,IAAA,OAAA+V,CAAA,CAAAgB,CAAAf,EAAAa,GAAA,GAAAnX,OAAA+V,sBAAA,KAAAuB,EAAAtX,OAAA+V,sBAAAO,GAAA,IAAAlQ,EAAA,EAAAA,EAAAkR,EAAAnR,OAAAC,IAAA9F,EAAAgX,EAAAlR,GAAA+Q,EAAAvX,QAAAU,IAAA,GAAAN,OAAAC,UAAAsX,qBAAApV,KAAAmU,EAAAhW,KAAA+V,EAAA/V,GAAAgW,EAAAhW,GAAA,QAAA+V,CAAA,UAAArN,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA7I,EAAA4B,GAAA,QAAA8C,EAAAiE,EAAA3I,GAAA4B,GAAA1B,EAAAwE,EAAAxE,KAAA,OAAAwD,GAAA,YAAAN,EAAAM,EAAA,CAAAgB,EAAAH,KAAApB,EAAAjD,GAAAwG,QAAAvD,QAAAjD,GAAAsD,KAAAoF,EAAAC,EAAA,UAAAC,EAAAnH,GAAA,sBAAA9C,EAAA,KAAAkK,EAAAC,UAAA,WAAAtC,SAAA,SAAAvD,EAAAC,GAAA,IAAAuF,EAAAhH,EAAAsH,MAAApK,EAAAkK,GAAA,SAAAH,EAAA1I,GAAAwI,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA,OAAA3I,EAAA,UAAA2I,EAAA/H,GAAA4H,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA,QAAA/H,EAAA,CAAA8H,OAAApJ,EAAA,KADA,IAAM0X,EAAe/N,EAAQ,KACvBgO,EAAchO,EAAQ,KACtB8J,EAAY9J,EAAQ,KAClB+J,EAAQ/J,EAAQ,KAAhB+J,IACFb,EAAQlJ,EAAQ,KAChBiO,EAAMjO,EAAQ,KACpBkO,EAOIlO,EAAQ,KANVmO,EAAcD,EAAdC,eACAC,EAAWF,EAAXE,YACAC,EAAeH,EAAfG,gBACAC,EAASJ,EAATI,UACAC,EAASL,EAATK,UACAC,EAAIN,EAAJM,KAGEC,EAAgB,EAEpBlZ,EAAOD,QAAOqK,EAAAP,IAAAlC,MAAG,SAAAgE,IAAA,IAAAb,EAAAqO,EAAAC,EAAAC,EAAAvF,EAAAwF,EAAAlM,EAAAmM,EAAAxO,EAAAyO,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAjP,EAAAkP,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAAAtQ,GAAAuQ,GAAAxP,GAAAJ,GAAA6P,GAAAC,GAAA7Q,UAAA,OAAAT,IAAAxH,MAAA,SAAAuJ,GAAA,cAAAA,EAAApD,KAAAoD,EAAA1F,MAAA,OAoQI,OApQG4E,EAAKqQ,GAAAhU,OAAA,QAAArG,IAAAqa,GAAA,GAAAA,GAAA,GAAG,MAAOhC,EAAGgC,GAAAhU,OAAA,QAAArG,IAAAqa,GAAA,GAAAA,GAAA,GAAGzC,EAAIxM,UAAWkN,EAAQ+B,GAAAhU,OAAA,QAAArG,IAAAqa,GAAA,GAAAA,GAAA,GAAG,CAAC,EAAG9B,EAAM8B,GAAAhU,OAAA,QAAArG,IAAAqa,GAAA,GAAAA,GAAA,GAAG,CAAC,EAC7ErH,EAAKH,EAAM,SAAUuF,GAAcI,EAKrCd,EAAYpB,EAAAA,EAAC,CAAC,EACbwB,GACAQ,IALHhM,EAAMkM,EAANlM,OACAmM,EAAYD,EAAZC,aACGxO,EAAOmN,EAAAoB,EAAA8B,GAKN5B,EAAW,CAAC,EACZC,EAAU,CAAC,EAIXC,EAAgC,iBAAV5O,EAAqBA,EAAMuQ,MAAM,KAAOvQ,EAChE6O,EAAaR,EACbS,EAAgBP,EACdQ,EAAe,CAACnB,EAAItM,QAASsM,EAAIxM,WAAWoP,SAASnC,KAASpO,EAAQwQ,WAItEvB,EAAY,IAAIhS,SAAQ,SAACvD,EAASC,GACtCqV,EAAmBtV,EACnBqV,EAAkBpV,CACpB,IACMuV,EAAc,SAACuB,GAAY1B,EAAgB0B,EAAMC,QAAU,GAE7DzQ,EAAS6N,EAAY9N,IAClB2Q,QAAUzB,EAEjBf,GAAiB,EAEXgB,EAAa,SAACyB,EAAW7D,GAC7B0B,EAASmC,GAAa7D,CACxB,EAEMqC,EAAY,SAACwB,EAAWC,GAC5BnC,EAAQkC,GAAaC,CACvB,EAEMxB,EAAW,SAAH/D,GAAA,IAAUwF,EAAKxF,EAATvC,GAAWC,EAAMsC,EAANtC,OAAQE,EAAOoC,EAAPpC,QAAO,OAC5C,IAAIjM,SAAQ,SAACvD,EAASC,GACpB8P,EAAI,IAADc,OAAKxB,EAAE,aAAAwB,OAAYuG,EAAK,aAAAvG,OAAYvB,IAEvC,IAAM4H,EAAY,GAAHrG,OAAMvB,EAAM,KAAAuB,OAAIuG,GAC/B3B,EAAWyB,EAAWlX,GACtB0V,EAAUwB,EAAWjX,GACrBuU,EAAKjO,EAAQ,CACX8Q,SAAUhI,EACV+H,MAAAA,EACA9H,OAAAA,EACAE,QAAAA,GAEJ,GAAE,EAGEoG,EAAO,WAAH,OACR0B,QAAQC,KAAK,sFAAsF,EAG/F1B,EAAe,SAACuB,GAAK,OACzBzB,EAAS7F,EAAU,CACjBT,GAAI+H,EAAO9H,OAAQ,OAAQE,QAAS,CAAElJ,QAAS,CAAEkR,SAAUpC,EAAcqC,SAAUnR,EAAQmR,SAAUC,QAASpR,EAAQoR,YACrH,EAGC5B,EAAY,SAAC6B,EAAMC,EAAMR,GAAK,OAClCzB,EAAS7F,EAAU,CACjBT,GAAI+H,EACJ9H,OAAQ,KACRE,QAAS,CAAE7P,OAAQ,YAAaiG,KAAM,CAAC+R,EAAMC,MAC5C,EAGC7B,EAAW,SAAC4B,EAAMP,GAAK,OAC3BzB,EAAS7F,EAAU,CACjBT,GAAI+H,EACJ9H,OAAQ,KACRE,QAAS,CAAE7P,OAAQ,WAAYiG,KAAM,CAAC+R,EAAM,CAAEE,SAAU,YACvD,EAGC7B,EAAa,SAAC2B,EAAMP,GAAK,OAC7BzB,EAAS7F,EAAU,CACjBT,GAAI+H,EACJ9H,OAAQ,KACRE,QAAS,CAAE7P,OAAQ,SAAUiG,KAAM,CAAC+R,MACnC,EAGC1B,EAAK,SAACtW,EAAQiG,EAAMwR,GAAK,OAC7BzB,EAAS7F,EAAU,CACjBT,GAAI+H,EACJ9H,OAAQ,KACRE,QAAS,CAAE7P,OAAAA,EAAQiG,KAAAA,KAClB,EAGCsQ,EAAe,WAAH,OAChBoB,QAAQC,KAAK,4GAA4G,EAGrHpB,EAAuB,SAAC2B,EAAQV,GAAK,OAAKzB,EAAS7F,EAAU,CACjET,GAAI+H,EACJ9H,OAAQ,eACRE,QAAS,CACPnJ,MAAOyR,EACPxR,QAAS,CACPyR,SAAUzR,EAAQyR,SAClBC,SAAU1R,EAAQ0R,SAClBC,UAAW3R,EAAQ2R,UACnBC,YAAa5R,EAAQ4R,YACrBC,KAAM7R,EAAQ6R,KACdX,SAAU,CAACvD,EAAItM,QAASsM,EAAIxM,WAAWoP,SAAS3B,KAC1C5O,EAAQ8R,eAGjB,EAEGhC,EAAa,WAAH,OACdkB,QAAQC,KAAK,iGAAiG,EAG1GlB,EAAqB,SAACyB,EAAQO,EAAMC,EAASlB,GAAK,OACtDzB,EAAS7F,EAAU,CACjBT,GAAI+H,EACJ9H,OAAQ,aACRE,QAAS,CAAEnJ,MAAOyR,EAAQpD,IAAK2D,EAAMzD,OAAQ0D,KAC5C,EAGChC,EAAe,WAAuC,IAAtCjQ,EAAKR,UAAAnD,OAAA,QAAArG,IAAAwJ,UAAA,GAAAA,UAAA,GAAG,MAAO6O,EAAG7O,UAAAnD,OAAA,EAAAmD,UAAA,QAAAxJ,EAAEuY,EAAM/O,UAAAnD,OAAA,EAAAmD,UAAA,QAAAxJ,EAAE+a,EAAKvR,UAAAnD,OAAA,EAAAmD,UAAA,QAAAxJ,EAErD,GAAI+Y,GAAgB,CAACnB,EAAIzM,eAAgByM,EAAIvM,yBAAyBmP,SAASnC,GAAM,MAAM/T,MAAM,4CAEjG,IAAM0X,EAAO3D,GAAOQ,EACpBA,EAAamD,EAEb,IAAMC,EAAU1D,GAAUO,EAC1BA,EAAgBmD,EAOhB,IAnKJ5I,EAoKUoI,GAD4B,iBAAVzR,EAAqBA,EAAMuQ,MAAM,KAAOvQ,GACxCmM,QAAO,SAAC+F,GAAC,OAAMtD,EAAa4B,SAAS0B,EAAE,IAG/D,OAFAtD,EAAa9S,KAAI2D,MAAjBmP,EArKJ,SAAAvF,GAAA,GAAAG,MAAAiB,QAAApB,GAAA,OAAAD,EAAAC,EAAA,CAAAqB,CAAArB,EAqKyBoI,IArKzB,SAAAtU,GAAA,uBAAAvG,QAAA,MAAAuG,EAAAvG,OAAAE,WAAA,MAAAqG,EAAA,qBAAAqM,MAAAmB,KAAAxN,EAAA,CAAAyN,CAAAvB,IAAA,SAAAwB,EAAAC,GAAA,GAAAD,EAAA,qBAAAA,EAAA,OAAAzB,EAAAyB,EAAAC,GAAA,IAAAC,EAAA7U,OAAAC,UAAA6U,SAAA3S,KAAAwS,GAAAjN,MAAA,uBAAAmN,GAAAF,EAAAlO,cAAAoO,EAAAF,EAAAlO,YAAAC,MAAA,QAAAmO,GAAA,QAAAA,EAAAvB,MAAAmB,KAAAE,GAAA,cAAAE,GAAA,2CAAAE,KAAAF,GAAA3B,EAAAyB,EAAAC,QAAA,GAAAI,CAAA7B,IAAA,qBAAApO,UAAA,wIAAAkQ,IAuKQsG,EAAOpV,OAAS,EACXyT,EAAqB2B,EAAQV,GACjC/W,MAAK,kBAAMgW,EAAmBhQ,EAAOgS,EAAMC,EAASlB,EAAM,IAGxDf,EAAmBhQ,EAAOgS,EAAMC,EAASlB,EAClD,EAEMb,GAAgB,WAAmB,OACvCZ,EAAS7F,EAAU,CACjBT,GAFqCxJ,UAAAnD,OAAA,EAAAmD,UAAA,QAAAxJ,EAGrCiT,OAAQ,gBACRE,QAAS,CAAEgJ,OAJc3S,UAAAnD,OAAA,QAAArG,IAAAwJ,UAAA,GAAAA,UAAA,GAAG,CAAC,KAK5B,EAGCI,GAAS,eAAAgB,EAAAtB,EAAAP,IAAAlC,MAAG,SAAAwD,EAAON,GAAK,IAAAqS,EAAAC,EAAAtB,EAAAuB,EAAA9S,UAAA,OAAAT,IAAAxH,MAAA,SAAA+I,GAAA,cAAAA,EAAA5C,KAAA4C,EAAAlF,MAAA,OAIjB,OAJmBgX,EAAIE,EAAAjW,OAAA,QAAArG,IAAAsc,EAAA,GAAAA,EAAA,GAAG,CAAC,EAAGD,EAAMC,EAAAjW,OAAA,QAAArG,IAAAsc,EAAA,GAAAA,EAAA,GAAG,CAClDC,QAAQ,EAAMhB,MAAM,EAAMiB,MAAM,EAAMC,KAAK,GAC1C1B,EAAKuB,EAAAjW,OAAA,EAAAiW,EAAA,QAAAtc,EAAAsK,EAAAgK,GACNgF,EAAQhP,EAAA8K,GAAC3B,EAASnJ,EAAA+K,GACZ0F,EAAKzQ,EAAAlF,KAAA,EAEe8S,EAAUnO,GAAM,OAAjC,OAAiCO,EAAAoS,GAAApS,EAAA3F,KAAA2F,EAAAqS,GAAWP,EAAI9R,EAAAsS,GAAEP,EAAM/R,EAAAuS,GAAA,CAApD9S,MAAKO,EAAAoS,GAA0BzS,QAAOK,EAAAqS,GAAQN,OAAM/R,EAAAsS,IAAAtS,EAAAwS,GAAA,CAF/D9J,GAAE1I,EAAA+K,GACFpC,OAAQ,YACRE,QAAO7I,EAAAuS,IAAAvS,EAAAyS,IAAA,EAAAzS,EAAA8K,IAAA9K,EAAAwS,IAAAxS,EAAAxF,OAAA,YAAAwF,EAAAgK,IAAAhK,EAAAyS,KAAA,yBAAAzS,EAAAzC,OAAA,GAAAwC,EAAA,KAEV,gBARcG,GAAA,OAAAI,EAAAnB,MAAA,KAAAD,UAAA,KAUT2Q,GAAS,WAA6D,IAA5D6C,EAAKxT,UAAAnD,OAAA,QAAArG,IAAAwJ,UAAA,GAAAA,UAAA,GAAG,uBAAwByT,EAAQzT,UAAAnD,OAAA,QAAArG,IAAAwJ,UAAA,IAAAA,UAAA,GAAUuR,EAAKvR,UAAAnD,OAAA,EAAAmD,UAAA,QAAAxJ,EAErE,OADAib,QAAQvH,IAAI,0FACL4F,EAAS7F,EAAU,CACxBT,GAAI+H,EACJ9H,OAAQ,SACRE,QAAS,CAAE6J,MAAAA,EAAOC,SAAAA,KAEtB,EAEMtS,GAAM,eAAAgL,EAAArM,EAAAP,IAAAlC,MAAG,SAAAiD,EAAOC,EAAOgR,GAAK,OAAAhS,IAAAxH,MAAA,SAAA4I,GAAA,cAAAA,EAAAzC,KAAAyC,EAAA/E,MAAA,WAC5B2T,EAAc,CAAF5O,EAAA/E,KAAA,cAAQd,MAAM,gEAA+D,OAGlF,OAHkF6F,EAAAmK,GAEtFgF,EAAQnP,EAAAiL,GAAC3B,EAAStJ,EAAAkL,GACnB0F,EAAK5Q,EAAA/E,KAAA,EAEe8S,EAAUnO,GAAM,OAAjC,OAAiCI,EAAAuS,GAAAvS,EAAAxF,KAAAwF,EAAAwS,GAAA,CAA7B5S,MAAKI,EAAAuS,IAAAvS,EAAAyS,GAAA,CAFhB5J,GAAE7I,EAAAkL,GACFpC,OAAQ,SACRE,QAAOhJ,EAAAwS,IAAAxS,EAAA0S,IAAA,EAAA1S,EAAAiL,IAAAjL,EAAAyS,IAAAzS,EAAArF,OAAA,YAAAqF,EAAAmK,IAAAnK,EAAA0S,KAAA,yBAAA1S,EAAAtC,OAAA,GAAAiC,EAAA,KAEV,gBARWW,EAAAC,GAAA,OAAAiL,EAAAlM,MAAA,KAAAD,UAAA,KAUNe,GAAS,eAAA2S,EAAA5T,EAAAP,IAAAlC,MAAG,SAAAkE,IAAA,OAAAhC,IAAAxH,MAAA,SAAAyJ,GAAA,cAAAA,EAAAtD,KAAAsD,EAAA5F,MAAA,OAUf,OATc,OAAX8E,IAOF8N,EAAgB9N,GAChBA,EAAS,MACVc,EAAAlG,OAAA,SACMoC,QAAQvD,WAAS,wBAAAqH,EAAAnD,OAAA,GAAAkD,EAAA,KACzB,kBAZc,OAAAmS,EAAAzT,MAAA,KAAAD,UAAA,KAcfyO,EAAU/N,GAAQ,SAAAiT,GAEZ,IADJnC,EAAQmC,EAARnC,SAAUD,EAAKoC,EAALpC,MAAOqC,EAAMD,EAANC,OAAQnK,EAAMkK,EAANlK,OAAQoK,EAAIF,EAAJE,KAE3BxC,EAAY,GAAHrG,OAAMvB,EAAM,KAAAuB,OAAIuG,GAC/B,GAAe,YAAXqC,EAAsB,CACxB1J,EAAI,IAADc,OAAKwG,EAAQ,gBAAAxG,OAAeuG,IAC/B,IAAIuC,EAAID,EACO,cAAXpK,EACFqK,EAAI3F,EAAY0F,GACI,WAAXpK,IACTqK,EAAI9J,MAAMmB,KAAI2B,EAAAA,EAAC,CAAC,EAAI+G,GAAI,IAAEhX,OAAQnG,OAAOkH,KAAKiW,GAAMhX,WAEtDqS,EAASmC,GAAW,CAAEE,MAAAA,EAAOsC,KAAMC,GACrC,MAAO,GAAe,WAAXF,EAAqB,CAG9B,GAFAzE,EAAQkC,GAAWwC,GACJ,SAAXpK,GAAmB+F,EAAgBqE,IACnC5E,EAGF,MAAMnU,MAAM+Y,GAFZ5E,EAAa4E,EAIjB,KAAsB,aAAXD,GACT9Q,EAAMgK,EAAAA,EAAC,CAAC,EAAI+G,GAAI,IAAEE,UAAWxC,IAEjC,IAEMX,GAAa,CACjBpH,GAAAA,EACA9I,OAAAA,EACAkP,WAAAA,EACAC,UAAAA,EACAE,KAAAA,EACAE,UAAAA,EACAC,SAAAA,EACAC,WAAAA,EACAC,GAAAA,EACAC,aAAAA,EACAE,WAAAA,EACAE,aAAAA,EACAC,cAAAA,GACAtQ,UAAAA,GACAuQ,OAAAA,GACAxP,OAAAA,GACAJ,UAAAA,IAGFiP,IACGxV,MAAK,kBAAM8V,EAAqB9P,EAAM,IACtChG,MAAK,kBAAMgW,EAAmBhQ,EAAOqO,EAAKE,EAAO,IACjDvU,MAAK,kBAAMiV,EAAiBmB,GAAW,IACvCnR,OAAM,WAAO,IAAG6B,EAAAhG,OAAA,SAEZoU,GAAS,yBAAApO,EAAAjD,OAAA,GAAAgD,EAAA,q3BC9QlBlB,EAAQ,KACR,IAAM6T,EAAkB7T,EAAQ,KAC1BD,EAAeC,EAAQ,KACvB8T,EAAY9T,EAAQ,KACpB+T,EAAY/T,EAAQ,GACpBiO,EAAMjO,EAAQ,KACdgU,EAAMhU,EAAQ,KACZiU,EAAejU,EAAQ,KAAvBiU,WAER1e,EAAOD,uWAAOqX,CAAA,CACZoH,UAAAA,EACA9F,IAAAA,EACA+F,IAAAA,EACAH,gBAAAA,EACA9T,aAAAA,EACAkU,WAAAA,GACGH,8sCCRLve,EAAOD,QAAU,SAAC4e,GAChB,IAAMtB,EAAS,GACTuB,EAAa,GACbC,EAAQ,GACRC,EAAQ,GACR9H,EAAU,GA8BhB,OA5BI2H,EAAKtB,QACPsB,EAAKtB,OAAOlZ,SAAQ,SAAC4a,GACnBA,EAAMH,WAAWza,SAAQ,SAAC6a,GACxBA,EAAUH,MAAM1a,SAAQ,SAAC8a,GACvBA,EAAKH,MAAM3a,SAAQ,SAAC+a,GAClBA,EAAKlI,QAAQ7S,SAAQ,SAAC+S,GACpBF,EAAQpQ,KAAIwQ,EAAAA,EAAC,CAAC,EACTF,GAAG,IAAEyH,KAAAA,EAAMI,MAAAA,EAAOC,UAAAA,EAAWC,KAAAA,EAAMC,KAAAA,IAE1C,IACAJ,EAAMlY,KAAIwQ,EAAAA,EAAC,CAAC,EACP8H,GAAI,IAAEP,KAAAA,EAAMI,MAAAA,EAAOC,UAAAA,EAAWC,KAAAA,IAErC,IACAJ,EAAMjY,KAAIwQ,EAAAA,EAAC,CAAC,EACP6H,GAAI,IAAEN,KAAAA,EAAMI,MAAAA,EAAOC,UAAAA,IAE1B,IACAJ,EAAWhY,KAAIwQ,EAAAA,EAAC,CAAC,EACZ4H,GAAS,IAAEL,KAAAA,EAAMI,MAAAA,IAExB,IACA1B,EAAOzW,KAAIwQ,EAAAA,EAAC,CAAC,EACR2H,GAAK,IAAEJ,KAAAA,IAEd,IAGFvH,EAAAA,EAAA,GACKuH,GAAI,IAAEtB,OAAAA,EAAQuB,WAAAA,EAAYC,MAAAA,EAAOC,MAAAA,EAAO9H,QAAAA,GAE/C,uPCvDA,IAAMmI,EAAa1U,EAAQ,KAE3BzK,EAAOD,QAAU,SAACuB,GAChB,IAAM8d,EAAM,CAAC,EAYb,MAViC,oBAAtBC,kBACTD,EAAI7e,KAAO,YACF4e,IACTC,EAAI7e,KAAO,WACkB,gCAAb+e,SAAQ,YAAAjf,EAARif,WAChBF,EAAI7e,KAAO,UACiB,gCAAZD,QAAO,YAAAD,EAAPC,YAChB8e,EAAI7e,KAAO,aAGM,IAARe,EACF8d,EAGFA,EAAI9d,EACb,WCpBAtB,EAAOD,QAAU,SAACwf,EAAQC,GAAG,SAAAlK,OACxBiK,EAAM,KAAAjK,OAAIkK,EAAG,KAAAlK,OAAImK,KAAKC,SAAS5J,SAAS,IAAIpN,MAAM,EAAG,GAAE,gCCDxDyT,GAAU,EAEdpc,EAAQoc,QAAUA,EAElBpc,EAAQ2e,WAAa,SAACiB,GACpBxD,EAAUwD,CACZ,EAEA5f,EAAQyU,IAAM,mBAAA8B,EAAAhM,UAAAnD,OAAIkD,EAAI,IAAAiK,MAAAgC,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJlM,EAAIkM,GAAAjM,UAAAiM,GAAA,OAAM4F,EAAUJ,QAAQvH,IAAIjK,MAAM8K,EAAMhL,GAAQ,IAAI,k3BCR1E,IAEMuV,EAFoD,YAAxCnV,EAAQ,IAARA,CAA4B,QAEf,SAAAoV,GAAC,OAAK,IAAIC,IAAID,EAAGzf,OAAO2f,SAASC,MAAOA,IAAI,EAAG,SAAAH,GAAC,OAAIA,CAAC,EAEpF7f,EAAOD,QAAU,SAACgL,GAChB,IAAMmS,iWAAI9F,CAAA,GAAQrM,GAMlB,MALA,CAAC,WAAY,aAAc,YAAY5G,SAAQ,SAAC7C,GAC1CyJ,EAAQzJ,KACV4b,EAAK5b,GAAOse,EAAW1C,EAAK5b,IAEhC,IACO4b,CACT,mtCCZA,IAAM+C,EAAUxV,EAAAA,KAAAA,GACVmO,EAAiBnO,EAAQ,KAK/BzK,EAAOD,QAAOqX,EAAAA,EAAA,GACTwB,GAAc,IACjBsH,WAAY,8CAAF5K,OAAgD2K,EAAO,wCCCnE,IAAMrH,EAAiBnO,EAAQ,KACzBoO,EAAcpO,EAAQ,KACtBqO,EAAkBrO,EAAQ,KAC1BsO,EAAYtO,EAAQ,KACpBwO,EAAOxO,EAAQ,KACfuO,EAAYvO,EAAQ,KAE1BzK,EAAOD,QAAU,CACf6Y,eAAAA,EACAC,YAAAA,EACAC,gBAAAA,EACAC,UAAAA,EACAE,KAAAA,EACAD,UAAAA,4QCrBFnP,EAAA,kBAAA9J,CAAA,MAAAA,EAAA,GAAAgB,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAI,gBAAA,SAAAC,EAAAC,EAAAC,GAAAF,EAAAC,GAAAC,EAAAC,KAAA,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAA/B,EAAAoB,EAAAC,EAAAE,GAAA,OAAAR,OAAAI,eAAAC,EAAAC,EAAA,CAAAE,MAAAA,EAAAS,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAd,EAAAC,EAAA,KAAArB,EAAA,aAAAmC,GAAAnC,EAAA,SAAAoB,EAAAC,EAAAE,GAAA,OAAAH,EAAAC,GAAAE,CAAA,WAAAa,EAAAC,EAAAC,EAAApC,EAAAqC,GAAA,IAAAC,EAAAF,GAAAA,EAAAtB,qBAAAyB,EAAAH,EAAAG,EAAAC,EAAA3B,OAAA4B,OAAAH,EAAAxB,WAAA4B,EAAA,IAAAC,EAAAN,GAAA,WAAApB,EAAAuB,EAAA,WAAAnB,MAAAuB,EAAAT,EAAAnC,EAAA0C,KAAAF,CAAA,UAAAK,EAAAC,EAAA5B,EAAA6B,GAAA,WAAA3C,KAAA,SAAA2C,IAAAD,EAAAE,KAAA9B,EAAA6B,GAAA,OAAAd,GAAA,OAAA7B,KAAA,QAAA2C,IAAAd,EAAA,EAAArC,EAAAsC,KAAAA,EAAA,IAAAmB,EAAA,YAAAd,IAAA,UAAAe,IAAA,UAAAC,IAAA,KAAAC,EAAA,GAAA1D,EAAA0D,EAAAhC,GAAA,8BAAAkC,EAAA7C,OAAA8C,eAAAC,EAAAF,GAAAA,EAAAA,EAAAG,EAAA,MAAAD,GAAAA,IAAAhD,GAAAG,EAAAiC,KAAAY,EAAApC,KAAAgC,EAAAI,GAAA,IAAAE,EAAAP,EAAAzC,UAAAyB,EAAAzB,UAAAD,OAAA4B,OAAAe,GAAA,SAAAO,EAAAjD,GAAA,0BAAAkD,SAAA,SAAAC,GAAAnE,EAAAgB,EAAAmD,GAAA,SAAAlB,GAAA,YAAAmB,QAAAD,EAAAlB,EAAA,gBAAAoB,EAAA3B,EAAA4B,GAAA,SAAAC,EAAAJ,EAAAlB,EAAAuB,EAAAC,GAAA,IAAAC,EAAA3B,EAAAL,EAAAyB,GAAAzB,EAAAO,GAAA,aAAAyB,EAAApE,KAAA,KAAAqE,EAAAD,EAAAzB,IAAA1B,EAAAoD,EAAApD,MAAA,OAAAA,GAAA,UAAAnB,EAAAmB,IAAAN,EAAAiC,KAAA3B,EAAA,WAAA+C,EAAAE,QAAAjD,EAAAqD,SAAAC,MAAA,SAAAtD,GAAAgD,EAAA,OAAAhD,EAAAiD,EAAAC,EAAA,aAAAtC,GAAAoC,EAAA,QAAApC,EAAAqC,EAAAC,EAAA,IAAAH,EAAAE,QAAAjD,GAAAsD,MAAA,SAAAC,GAAAH,EAAApD,MAAAuD,EAAAN,EAAAG,EAAA,aAAAI,GAAA,OAAAR,EAAA,QAAAQ,EAAAP,EAAAC,EAAA,IAAAA,EAAAC,EAAAzB,IAAA,KAAA+B,EAAA7D,EAAA,gBAAAI,MAAA,SAAA4C,EAAAlB,GAAA,SAAAgC,IAAA,WAAAX,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAlB,EAAAuB,EAAAC,EAAA,WAAAO,EAAAA,EAAAA,EAAAH,KAAAI,EAAAA,GAAAA,GAAA,aAAAnC,EAAAT,EAAAnC,EAAA0C,GAAA,IAAAsC,EAAA,iCAAAf,EAAAlB,GAAA,iBAAAiC,EAAA,UAAAC,MAAA,iDAAAD,EAAA,cAAAf,EAAA,MAAAlB,EAAA,OAAA1B,WAAAV,EAAA+E,MAAA,OAAAhD,EAAAuB,OAAAA,EAAAvB,EAAAK,IAAAA,IAAA,KAAAoC,EAAAzC,EAAAyC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAAzC,GAAA,GAAA0C,EAAA,IAAAA,IAAA/B,EAAA,gBAAA+B,CAAA,cAAA1C,EAAAuB,OAAAvB,EAAA4C,KAAA5C,EAAA6C,MAAA7C,EAAAK,SAAA,aAAAL,EAAAuB,OAAA,uBAAAe,EAAA,MAAAA,EAAA,YAAAtC,EAAAK,IAAAL,EAAA8C,kBAAA9C,EAAAK,IAAA,gBAAAL,EAAAuB,QAAAvB,EAAA+C,OAAA,SAAA/C,EAAAK,KAAAiC,EAAA,gBAAAR,EAAA3B,EAAAV,EAAAnC,EAAA0C,GAAA,cAAA8B,EAAApE,KAAA,IAAA4E,EAAAtC,EAAAgD,KAAA,6BAAAlB,EAAAzB,MAAAM,EAAA,gBAAAhC,MAAAmD,EAAAzB,IAAA2C,KAAAhD,EAAAgD,KAAA,WAAAlB,EAAApE,OAAA4E,EAAA,YAAAtC,EAAAuB,OAAA,QAAAvB,EAAAK,IAAAyB,EAAAzB,IAAA,YAAAsC,EAAAF,EAAAzC,GAAA,IAAAiD,EAAAjD,EAAAuB,OAAAA,EAAAkB,EAAA1D,SAAAkE,GAAA,QAAAhF,IAAAsD,EAAA,OAAAvB,EAAAyC,SAAA,eAAAQ,GAAAR,EAAA1D,SAAAkI,SAAAjH,EAAAuB,OAAA,SAAAvB,EAAAK,SAAApC,EAAA0E,EAAAF,EAAAzC,GAAA,UAAAA,EAAAuB,SAAA,WAAA0B,IAAAjD,EAAAuB,OAAA,QAAAvB,EAAAK,IAAA,IAAA6C,UAAA,oCAAAD,EAAA,aAAAtC,EAAA,IAAAmB,EAAA3B,EAAAoB,EAAAkB,EAAA1D,SAAAiB,EAAAK,KAAA,aAAAyB,EAAApE,KAAA,OAAAsC,EAAAuB,OAAA,QAAAvB,EAAAK,IAAAyB,EAAAzB,IAAAL,EAAAyC,SAAA,KAAA9B,EAAA,IAAAwC,EAAArB,EAAAzB,IAAA,OAAA8C,EAAAA,EAAAH,MAAAhD,EAAAyC,EAAAW,YAAAD,EAAAxE,MAAAqB,EAAAqD,KAAAZ,EAAAa,QAAA,WAAAtD,EAAAuB,SAAAvB,EAAAuB,OAAA,OAAAvB,EAAAK,SAAApC,GAAA+B,EAAAyC,SAAA,KAAA9B,GAAAwC,GAAAnD,EAAAuB,OAAA,QAAAvB,EAAAK,IAAA,IAAA6C,UAAA,oCAAAlD,EAAAyC,SAAA,KAAA9B,EAAA,UAAA4C,EAAAC,GAAA,IAAAC,EAAA,CAAAC,OAAAF,EAAA,SAAAA,IAAAC,EAAAE,SAAAH,EAAA,SAAAA,IAAAC,EAAAG,WAAAJ,EAAA,GAAAC,EAAAI,SAAAL,EAAA,SAAAM,WAAAC,KAAAN,EAAA,UAAAO,EAAAP,GAAA,IAAA3B,EAAA2B,EAAAQ,YAAA,GAAAnC,EAAApE,KAAA,gBAAAoE,EAAAzB,IAAAoD,EAAAQ,WAAAnC,CAAA,UAAA7B,EAAAN,GAAA,KAAAmE,WAAA,EAAAJ,OAAA,SAAA/D,EAAA2B,QAAAiC,EAAA,WAAAW,OAAA,YAAA/C,EAAAgD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAArF,GAAA,GAAAsF,EAAA,OAAAA,EAAA9D,KAAA6D,GAAA,sBAAAA,EAAAd,KAAA,OAAAc,EAAA,IAAAE,MAAAF,EAAAG,QAAA,KAAAC,GAAA,EAAAlB,EAAA,SAAAA,IAAA,OAAAkB,EAAAJ,EAAAG,QAAA,GAAAjG,EAAAiC,KAAA6D,EAAAI,GAAA,OAAAlB,EAAA1E,MAAAwF,EAAAI,GAAAlB,EAAAL,MAAA,EAAAK,EAAA,OAAAA,EAAA1E,WAAAV,EAAAoF,EAAAL,MAAA,EAAAK,CAAA,SAAAA,EAAAA,KAAAA,CAAA,SAAAA,KAAAb,EAAA,UAAAA,IAAA,OAAA7D,WAAAV,EAAA+E,MAAA,UAAApC,EAAAxC,UAAAyC,EAAAtC,EAAA6C,EAAA,eAAAzC,MAAAkC,EAAAxB,cAAA,IAAAd,EAAAsC,EAAA,eAAAlC,MAAAiC,EAAAvB,cAAA,IAAAuB,EAAA4D,YAAApH,EAAAyD,EAAA3B,EAAA,qBAAAhC,EAAAuH,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,GAAAA,EAAAE,YAAA,QAAAD,IAAAA,IAAA/D,GAAA,uBAAA+D,EAAAH,aAAAG,EAAAE,MAAA,EAAA3H,EAAA4H,KAAA,SAAAJ,GAAA,OAAAvG,OAAA4G,eAAA5G,OAAA4G,eAAAL,EAAA7D,IAAA6D,EAAAM,UAAAnE,EAAAzD,EAAAsH,EAAAxF,EAAA,sBAAAwF,EAAAtG,UAAAD,OAAA4B,OAAAqB,GAAAsD,CAAA,EAAAxH,EAAA+H,MAAA,SAAA5E,GAAA,OAAA2B,QAAA3B,EAAA,EAAAgB,EAAAI,EAAArD,WAAAhB,EAAAqE,EAAArD,UAAAY,GAAA,0BAAA9B,EAAAuE,cAAAA,EAAAvE,EAAAgI,MAAA,SAAAzF,EAAAC,EAAApC,EAAAqC,EAAA+B,QAAA,IAAAA,IAAAA,EAAAyD,SAAA,IAAAC,EAAA,IAAA3D,EAAAjC,EAAAC,EAAAC,EAAApC,EAAAqC,GAAA+B,GAAA,OAAAxE,EAAAuH,oBAAA/E,GAAA0F,EAAAA,EAAA/B,OAAApB,MAAA,SAAAF,GAAA,OAAAA,EAAAiB,KAAAjB,EAAApD,MAAAyG,EAAA/B,MAAA,KAAAhC,EAAAD,GAAAhE,EAAAgE,EAAAlC,EAAA,aAAA9B,EAAAgE,EAAAtC,GAAA,0BAAA1B,EAAAgE,EAAA,qDAAAlE,EAAAmI,KAAA,SAAAC,GAAA,IAAAC,EAAApH,OAAAmH,GAAAD,EAAA,WAAA5G,KAAA8G,EAAAF,EAAAtB,KAAAtF,GAAA,OAAA4G,EAAAG,UAAA,SAAAnC,IAAA,KAAAgC,EAAAf,QAAA,KAAA7F,EAAA4G,EAAAI,MAAA,GAAAhH,KAAA8G,EAAA,OAAAlC,EAAA1E,MAAAF,EAAA4E,EAAAL,MAAA,EAAAK,CAAA,QAAAA,EAAAL,MAAA,EAAAK,CAAA,GAAAnG,EAAAiE,OAAAA,EAAAlB,EAAA7B,UAAA,CAAAwG,YAAA3E,EAAAiE,MAAA,SAAAwB,GAAA,QAAAC,KAAA,OAAAtC,KAAA,OAAAT,KAAA,KAAAC,WAAA5E,EAAA,KAAA+E,MAAA,OAAAP,SAAA,UAAAlB,OAAA,YAAAlB,SAAApC,EAAA,KAAA6F,WAAAxC,QAAA0C,IAAA0B,EAAA,QAAAb,KAAA,WAAAA,EAAAe,OAAA,IAAAvH,EAAAiC,KAAA,KAAAuE,KAAAR,OAAAQ,EAAAgB,MAAA,WAAAhB,QAAA5G,EAAA,EAAA6H,KAAA,gBAAA9C,MAAA,MAAA+C,EAAA,KAAAjC,WAAA,GAAAG,WAAA,aAAA8B,EAAArI,KAAA,MAAAqI,EAAA1F,IAAA,YAAA2F,IAAA,EAAAlD,kBAAA,SAAAmD,GAAA,QAAAjD,KAAA,MAAAiD,EAAA,IAAAjG,EAAA,cAAAkG,EAAAC,EAAAC,GAAA,OAAAtE,EAAApE,KAAA,QAAAoE,EAAAzB,IAAA4F,EAAAjG,EAAAqD,KAAA8C,EAAAC,IAAApG,EAAAuB,OAAA,OAAAvB,EAAAK,SAAApC,KAAAmI,CAAA,SAAA7B,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAAzC,EAAA2B,EAAAQ,WAAA,YAAAR,EAAAC,OAAA,OAAAwC,EAAA,UAAAzC,EAAAC,QAAA,KAAAiC,KAAA,KAAAU,EAAAhI,EAAAiC,KAAAmD,EAAA,YAAA6C,EAAAjI,EAAAiC,KAAAmD,EAAA,iBAAA4C,GAAAC,EAAA,SAAAX,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,WAAAgC,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,WAAA,SAAAyC,GAAA,QAAAV,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,YAAA2C,EAAA,UAAA/D,MAAA,kDAAAoD,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,WAAA,KAAAb,OAAA,SAAArF,EAAA2C,GAAA,QAAAkE,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,QAAA,KAAAiC,MAAAtH,EAAAiC,KAAAmD,EAAA,oBAAAkC,KAAAlC,EAAAG,WAAA,KAAA2C,EAAA9C,EAAA,OAAA8C,IAAA,UAAA7I,GAAA,aAAAA,IAAA6I,EAAA7C,QAAArD,GAAAA,GAAAkG,EAAA3C,aAAA2C,EAAA,UAAAzE,EAAAyE,EAAAA,EAAAtC,WAAA,UAAAnC,EAAApE,KAAAA,EAAAoE,EAAAzB,IAAAA,EAAAkG,GAAA,KAAAhF,OAAA,YAAA8B,KAAAkD,EAAA3C,WAAAjD,GAAA,KAAA6F,SAAA1E,EAAA,EAAA0E,SAAA,SAAA1E,EAAA+B,GAAA,aAAA/B,EAAApE,KAAA,MAAAoE,EAAAzB,IAAA,gBAAAyB,EAAApE,MAAA,aAAAoE,EAAApE,KAAA,KAAA2F,KAAAvB,EAAAzB,IAAA,WAAAyB,EAAApE,MAAA,KAAAsI,KAAA,KAAA3F,IAAAyB,EAAAzB,IAAA,KAAAkB,OAAA,cAAA8B,KAAA,kBAAAvB,EAAApE,MAAAmG,IAAA,KAAAR,KAAAQ,GAAAlD,CAAA,EAAA8F,OAAA,SAAA7C,GAAA,QAAAW,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAG,aAAAA,EAAA,YAAA4C,SAAA/C,EAAAQ,WAAAR,EAAAI,UAAAG,EAAAP,GAAA9C,CAAA,GAAAuG,MAAA,SAAAxD,GAAA,QAAAa,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,SAAAA,EAAA,KAAA5B,EAAA2B,EAAAQ,WAAA,aAAAnC,EAAApE,KAAA,KAAAgJ,EAAA5E,EAAAzB,IAAA2D,EAAAP,EAAA,QAAAiD,CAAA,YAAAnE,MAAA,0BAAAoE,cAAA,SAAAxC,EAAAf,EAAAE,GAAA,YAAAb,SAAA,CAAA1D,SAAAoC,EAAAgD,GAAAf,WAAAA,EAAAE,QAAAA,GAAA,cAAA/B,SAAA,KAAAlB,SAAApC,GAAA0C,CAAA,GAAAzD,CAAA,UAAAiK,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA7I,EAAA4B,GAAA,QAAA8C,EAAAiE,EAAA3I,GAAA4B,GAAA1B,EAAAwE,EAAAxE,KAAA,OAAAwD,GAAA,YAAAN,EAAAM,EAAA,CAAAgB,EAAAH,KAAApB,EAAAjD,GAAAwG,QAAAvD,QAAAjD,GAAAsD,KAAAoF,EAAAC,EAAA,UAAAC,EAAAnH,GAAA,sBAAA9C,EAAA,KAAAkK,EAAAC,UAAA,WAAAtC,SAAA,SAAAvD,EAAAC,GAAA,IAAAuF,EAAAhH,EAAAsH,MAAApK,EAAAkK,GAAA,SAAAH,EAAA1I,GAAAwI,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA,OAAA3I,EAAA,UAAA2I,EAAA/H,GAAA4H,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA,QAAA/H,EAAA,CAAA8H,OAAApJ,EAAA,KAMA,IAAMqf,EAAqB,SAACC,GAAI,OAC9B,IAAIpY,SAAQ,SAACvD,EAASC,GACpB,IAAM2b,EAAa,IAAIC,WACvBD,EAAWE,OAAS,WAClB9b,EAAQ4b,EAAWzb,OACrB,EACAyb,EAAW3E,QAAU,SAAA/Q,GAAqC,IAAf6V,EAAI7V,EAAvB0M,OAAUrS,MAASwb,KACzC9b,EAAOU,MAAM,gCAADkQ,OAAiCkL,IAC/C,EACAH,EAAWI,kBAAkBL,EAC/B,GAAE,EAUEpH,EAAS,eAAA3C,EAAAjM,EAAAP,IAAAlC,MAAG,SAAAiD,EAAOC,GAAK,IAAAsT,EAAAuC,EAAAN,EAAA,OAAAvW,IAAAxH,MAAA,SAAA4I,GAAA,cAAAA,EAAAzC,KAAAyC,EAAA/E,MAAA,OACZ,GAAZiY,EAAOtT,OACU,IAAVA,EAAqB,CAAAI,EAAA/E,KAAA,eAAA+E,EAAArF,OAAA,SACvB,aAAW,UAGC,iBAAViF,EAAkB,CAAAI,EAAA/E,KAAA,aAEvB,yCAAyC6P,KAAKlL,GAAQ,CAAFI,EAAA/E,KAAA,QACtDiY,EAAOwC,KAAK9V,EAAMwQ,MAAM,KAAK,IAC1BA,MAAM,IACNuF,KAAI,SAACC,GAAC,OAAKA,EAAEC,WAAW,EAAE,IAAE7V,EAAA/E,KAAA,uBAAA+E,EAAA/E,KAAA,GAEZ6a,MAAMlW,GAAM,QAArB,OAAJ6V,EAAIzV,EAAAxF,KAAAwF,EAAA/E,KAAG,GACAwa,EAAKM,cAAa,QAA/B7C,EAAIlT,EAAAxF,KAAA,QAAAwF,EAAA/E,KAAG,GAAH,mBAE0B,oBAAhB+a,aAA+BpW,aAAiBoW,aAAW,CAAAhW,EAAA/E,KAAA,YACrD,QAAlB2E,EAAMqW,QAAiB,CAAAjW,EAAA/E,KAAA,gBAAA+E,EAAA/E,KAAA,GACZ8S,EAAUnO,EAAMsW,KAAI,QAAjChD,EAAIlT,EAAAxF,KAAA,WAEgB,UAAlBoF,EAAMqW,QAAmB,CAAAjW,EAAA/E,KAAA,gBAAA+E,EAAA/E,KAAA,GACd8S,EAAUnO,EAAMuW,QAAO,QAApCjD,EAAIlT,EAAAxF,KAAA,WAEgB,WAAlBoF,EAAMqW,QAAoB,CAAAjW,EAAA/E,KAAA,gBAAA+E,EAAA/E,KAAA,GACtB,IAAI8B,SAAQ,SAACvD,GACjBoG,EAAMwW,OAAM,eAAA3V,EAAAtB,EAAAP,IAAAlC,MAAC,SAAAwD,EAAOiV,GAAI,OAAAvW,IAAAxH,MAAA,SAAA+I,GAAA,cAAAA,EAAA5C,KAAA4C,EAAAlF,MAAA,cAAAkF,EAAAlF,KAAA,EACTia,EAAmBC,GAAK,OAArCjC,EAAI/S,EAAA3F,KACJhB,IAAU,wBAAA2G,EAAAzC,OAAA,GAAAwC,EAAA,KACX,gBAAAI,GAAA,OAAAG,EAAAnB,MAAA,KAAAD,UAAA,EAHW,GAId,IAAE,QAAAW,EAAA/E,KAAA,sBAEgC,oBAApBob,iBAAmCzW,aAAiByW,iBAAe,CAAArW,EAAA/E,KAAA,gBAAA+E,EAAA/E,KAAA,GAChE2E,EAAM0W,gBAAe,QAA9B,OAAJnB,EAAInV,EAAAxF,KAAAwF,EAAA/E,KAAG,GACAia,EAAmBC,GAAK,QAArCjC,EAAIlT,EAAAxF,KAAAwF,EAAA/E,KAAG,GAAH,mBACK2E,aAAiB2W,MAAQ3W,aAAiB4W,MAAI,CAAAxW,EAAA/E,KAAA,gBAAA+E,EAAA/E,KAAA,GAC1Cia,EAAmBtV,GAAM,QAAtCsT,EAAIlT,EAAAxF,KAAA,eAAAwF,EAAArF,OAAA,SAGC,IAAI8b,WAAWvD,IAAK,yBAAAlT,EAAAtC,OAAA,GAAAiC,EAAA,KAC5B,gBAvCcU,GAAA,OAAA+K,EAAA9L,MAAA,KAAAD,UAAA,KAyCftK,EAAOD,QAAUiZ,WCpEjBhZ,EAAOD,QAAU,SAACiL,EAAQ2W,GACxB3W,EAAO4W,UAAY,SAAAjX,GAAc,IAAXwT,EAAIxT,EAAJwT,KACpBwD,EAAQxD,EACV,CACF,2QCHAtU,EAAA,kBAAA9J,CAAA,MAAAA,EAAA,GAAAgB,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAI,gBAAA,SAAAC,EAAAC,EAAAC,GAAAF,EAAAC,GAAAC,EAAAC,KAAA,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAA/B,EAAAoB,EAAAC,EAAAE,GAAA,OAAAR,OAAAI,eAAAC,EAAAC,EAAA,CAAAE,MAAAA,EAAAS,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAd,EAAAC,EAAA,KAAArB,EAAA,aAAAmC,GAAAnC,EAAA,SAAAoB,EAAAC,EAAAE,GAAA,OAAAH,EAAAC,GAAAE,CAAA,WAAAa,EAAAC,EAAAC,EAAApC,EAAAqC,GAAA,IAAAC,EAAAF,GAAAA,EAAAtB,qBAAAyB,EAAAH,EAAAG,EAAAC,EAAA3B,OAAA4B,OAAAH,EAAAxB,WAAA4B,EAAA,IAAAC,EAAAN,GAAA,WAAApB,EAAAuB,EAAA,WAAAnB,MAAAuB,EAAAT,EAAAnC,EAAA0C,KAAAF,CAAA,UAAAK,EAAAC,EAAA5B,EAAA6B,GAAA,WAAA3C,KAAA,SAAA2C,IAAAD,EAAAE,KAAA9B,EAAA6B,GAAA,OAAAd,GAAA,OAAA7B,KAAA,QAAA2C,IAAAd,EAAA,EAAArC,EAAAsC,KAAAA,EAAA,IAAAmB,EAAA,YAAAd,IAAA,UAAAe,IAAA,UAAAC,IAAA,KAAAC,EAAA,GAAA1D,EAAA0D,EAAAhC,GAAA,8BAAAkC,EAAA7C,OAAA8C,eAAAC,EAAAF,GAAAA,EAAAA,EAAAG,EAAA,MAAAD,GAAAA,IAAAhD,GAAAG,EAAAiC,KAAAY,EAAApC,KAAAgC,EAAAI,GAAA,IAAAE,EAAAP,EAAAzC,UAAAyB,EAAAzB,UAAAD,OAAA4B,OAAAe,GAAA,SAAAO,EAAAjD,GAAA,0BAAAkD,SAAA,SAAAC,GAAAnE,EAAAgB,EAAAmD,GAAA,SAAAlB,GAAA,YAAAmB,QAAAD,EAAAlB,EAAA,gBAAAoB,EAAA3B,EAAA4B,GAAA,SAAAC,EAAAJ,EAAAlB,EAAAuB,EAAAC,GAAA,IAAAC,EAAA3B,EAAAL,EAAAyB,GAAAzB,EAAAO,GAAA,aAAAyB,EAAApE,KAAA,KAAAqE,EAAAD,EAAAzB,IAAA1B,EAAAoD,EAAApD,MAAA,OAAAA,GAAA,UAAAnB,EAAAmB,IAAAN,EAAAiC,KAAA3B,EAAA,WAAA+C,EAAAE,QAAAjD,EAAAqD,SAAAC,MAAA,SAAAtD,GAAAgD,EAAA,OAAAhD,EAAAiD,EAAAC,EAAA,aAAAtC,GAAAoC,EAAA,QAAApC,EAAAqC,EAAAC,EAAA,IAAAH,EAAAE,QAAAjD,GAAAsD,MAAA,SAAAC,GAAAH,EAAApD,MAAAuD,EAAAN,EAAAG,EAAA,aAAAI,GAAA,OAAAR,EAAA,QAAAQ,EAAAP,EAAAC,EAAA,IAAAA,EAAAC,EAAAzB,IAAA,KAAA+B,EAAA7D,EAAA,gBAAAI,MAAA,SAAA4C,EAAAlB,GAAA,SAAAgC,IAAA,WAAAX,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAlB,EAAAuB,EAAAC,EAAA,WAAAO,EAAAA,EAAAA,EAAAH,KAAAI,EAAAA,GAAAA,GAAA,aAAAnC,EAAAT,EAAAnC,EAAA0C,GAAA,IAAAsC,EAAA,iCAAAf,EAAAlB,GAAA,iBAAAiC,EAAA,UAAAC,MAAA,iDAAAD,EAAA,cAAAf,EAAA,MAAAlB,EAAA,OAAA1B,WAAAV,EAAA+E,MAAA,OAAAhD,EAAAuB,OAAAA,EAAAvB,EAAAK,IAAAA,IAAA,KAAAoC,EAAAzC,EAAAyC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAAzC,GAAA,GAAA0C,EAAA,IAAAA,IAAA/B,EAAA,gBAAA+B,CAAA,cAAA1C,EAAAuB,OAAAvB,EAAA4C,KAAA5C,EAAA6C,MAAA7C,EAAAK,SAAA,aAAAL,EAAAuB,OAAA,uBAAAe,EAAA,MAAAA,EAAA,YAAAtC,EAAAK,IAAAL,EAAA8C,kBAAA9C,EAAAK,IAAA,gBAAAL,EAAAuB,QAAAvB,EAAA+C,OAAA,SAAA/C,EAAAK,KAAAiC,EAAA,gBAAAR,EAAA3B,EAAAV,EAAAnC,EAAA0C,GAAA,cAAA8B,EAAApE,KAAA,IAAA4E,EAAAtC,EAAAgD,KAAA,6BAAAlB,EAAAzB,MAAAM,EAAA,gBAAAhC,MAAAmD,EAAAzB,IAAA2C,KAAAhD,EAAAgD,KAAA,WAAAlB,EAAApE,OAAA4E,EAAA,YAAAtC,EAAAuB,OAAA,QAAAvB,EAAAK,IAAAyB,EAAAzB,IAAA,YAAAsC,EAAAF,EAAAzC,GAAA,IAAAiD,EAAAjD,EAAAuB,OAAAA,EAAAkB,EAAA1D,SAAAkE,GAAA,QAAAhF,IAAAsD,EAAA,OAAAvB,EAAAyC,SAAA,eAAAQ,GAAAR,EAAA1D,SAAAkI,SAAAjH,EAAAuB,OAAA,SAAAvB,EAAAK,SAAApC,EAAA0E,EAAAF,EAAAzC,GAAA,UAAAA,EAAAuB,SAAA,WAAA0B,IAAAjD,EAAAuB,OAAA,QAAAvB,EAAAK,IAAA,IAAA6C,UAAA,oCAAAD,EAAA,aAAAtC,EAAA,IAAAmB,EAAA3B,EAAAoB,EAAAkB,EAAA1D,SAAAiB,EAAAK,KAAA,aAAAyB,EAAApE,KAAA,OAAAsC,EAAAuB,OAAA,QAAAvB,EAAAK,IAAAyB,EAAAzB,IAAAL,EAAAyC,SAAA,KAAA9B,EAAA,IAAAwC,EAAArB,EAAAzB,IAAA,OAAA8C,EAAAA,EAAAH,MAAAhD,EAAAyC,EAAAW,YAAAD,EAAAxE,MAAAqB,EAAAqD,KAAAZ,EAAAa,QAAA,WAAAtD,EAAAuB,SAAAvB,EAAAuB,OAAA,OAAAvB,EAAAK,SAAApC,GAAA+B,EAAAyC,SAAA,KAAA9B,GAAAwC,GAAAnD,EAAAuB,OAAA,QAAAvB,EAAAK,IAAA,IAAA6C,UAAA,oCAAAlD,EAAAyC,SAAA,KAAA9B,EAAA,UAAA4C,EAAAC,GAAA,IAAAC,EAAA,CAAAC,OAAAF,EAAA,SAAAA,IAAAC,EAAAE,SAAAH,EAAA,SAAAA,IAAAC,EAAAG,WAAAJ,EAAA,GAAAC,EAAAI,SAAAL,EAAA,SAAAM,WAAAC,KAAAN,EAAA,UAAAO,EAAAP,GAAA,IAAA3B,EAAA2B,EAAAQ,YAAA,GAAAnC,EAAApE,KAAA,gBAAAoE,EAAAzB,IAAAoD,EAAAQ,WAAAnC,CAAA,UAAA7B,EAAAN,GAAA,KAAAmE,WAAA,EAAAJ,OAAA,SAAA/D,EAAA2B,QAAAiC,EAAA,WAAAW,OAAA,YAAA/C,EAAAgD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAArF,GAAA,GAAAsF,EAAA,OAAAA,EAAA9D,KAAA6D,GAAA,sBAAAA,EAAAd,KAAA,OAAAc,EAAA,IAAAE,MAAAF,EAAAG,QAAA,KAAAC,GAAA,EAAAlB,EAAA,SAAAA,IAAA,OAAAkB,EAAAJ,EAAAG,QAAA,GAAAjG,EAAAiC,KAAA6D,EAAAI,GAAA,OAAAlB,EAAA1E,MAAAwF,EAAAI,GAAAlB,EAAAL,MAAA,EAAAK,EAAA,OAAAA,EAAA1E,WAAAV,EAAAoF,EAAAL,MAAA,EAAAK,CAAA,SAAAA,EAAAA,KAAAA,CAAA,SAAAA,KAAAb,EAAA,UAAAA,IAAA,OAAA7D,WAAAV,EAAA+E,MAAA,UAAApC,EAAAxC,UAAAyC,EAAAtC,EAAA6C,EAAA,eAAAzC,MAAAkC,EAAAxB,cAAA,IAAAd,EAAAsC,EAAA,eAAAlC,MAAAiC,EAAAvB,cAAA,IAAAuB,EAAA4D,YAAApH,EAAAyD,EAAA3B,EAAA,qBAAAhC,EAAAuH,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,GAAAA,EAAAE,YAAA,QAAAD,IAAAA,IAAA/D,GAAA,uBAAA+D,EAAAH,aAAAG,EAAAE,MAAA,EAAA3H,EAAA4H,KAAA,SAAAJ,GAAA,OAAAvG,OAAA4G,eAAA5G,OAAA4G,eAAAL,EAAA7D,IAAA6D,EAAAM,UAAAnE,EAAAzD,EAAAsH,EAAAxF,EAAA,sBAAAwF,EAAAtG,UAAAD,OAAA4B,OAAAqB,GAAAsD,CAAA,EAAAxH,EAAA+H,MAAA,SAAA5E,GAAA,OAAA2B,QAAA3B,EAAA,EAAAgB,EAAAI,EAAArD,WAAAhB,EAAAqE,EAAArD,UAAAY,GAAA,0BAAA9B,EAAAuE,cAAAA,EAAAvE,EAAAgI,MAAA,SAAAzF,EAAAC,EAAApC,EAAAqC,EAAA+B,QAAA,IAAAA,IAAAA,EAAAyD,SAAA,IAAAC,EAAA,IAAA3D,EAAAjC,EAAAC,EAAAC,EAAApC,EAAAqC,GAAA+B,GAAA,OAAAxE,EAAAuH,oBAAA/E,GAAA0F,EAAAA,EAAA/B,OAAApB,MAAA,SAAAF,GAAA,OAAAA,EAAAiB,KAAAjB,EAAApD,MAAAyG,EAAA/B,MAAA,KAAAhC,EAAAD,GAAAhE,EAAAgE,EAAAlC,EAAA,aAAA9B,EAAAgE,EAAAtC,GAAA,0BAAA1B,EAAAgE,EAAA,qDAAAlE,EAAAmI,KAAA,SAAAC,GAAA,IAAAC,EAAApH,OAAAmH,GAAAD,EAAA,WAAA5G,KAAA8G,EAAAF,EAAAtB,KAAAtF,GAAA,OAAA4G,EAAAG,UAAA,SAAAnC,IAAA,KAAAgC,EAAAf,QAAA,KAAA7F,EAAA4G,EAAAI,MAAA,GAAAhH,KAAA8G,EAAA,OAAAlC,EAAA1E,MAAAF,EAAA4E,EAAAL,MAAA,EAAAK,CAAA,QAAAA,EAAAL,MAAA,EAAAK,CAAA,GAAAnG,EAAAiE,OAAAA,EAAAlB,EAAA7B,UAAA,CAAAwG,YAAA3E,EAAAiE,MAAA,SAAAwB,GAAA,QAAAC,KAAA,OAAAtC,KAAA,OAAAT,KAAA,KAAAC,WAAA5E,EAAA,KAAA+E,MAAA,OAAAP,SAAA,UAAAlB,OAAA,YAAAlB,SAAApC,EAAA,KAAA6F,WAAAxC,QAAA0C,IAAA0B,EAAA,QAAAb,KAAA,WAAAA,EAAAe,OAAA,IAAAvH,EAAAiC,KAAA,KAAAuE,KAAAR,OAAAQ,EAAAgB,MAAA,WAAAhB,QAAA5G,EAAA,EAAA6H,KAAA,gBAAA9C,MAAA,MAAA+C,EAAA,KAAAjC,WAAA,GAAAG,WAAA,aAAA8B,EAAArI,KAAA,MAAAqI,EAAA1F,IAAA,YAAA2F,IAAA,EAAAlD,kBAAA,SAAAmD,GAAA,QAAAjD,KAAA,MAAAiD,EAAA,IAAAjG,EAAA,cAAAkG,EAAAC,EAAAC,GAAA,OAAAtE,EAAApE,KAAA,QAAAoE,EAAAzB,IAAA4F,EAAAjG,EAAAqD,KAAA8C,EAAAC,IAAApG,EAAAuB,OAAA,OAAAvB,EAAAK,SAAApC,KAAAmI,CAAA,SAAA7B,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAAzC,EAAA2B,EAAAQ,WAAA,YAAAR,EAAAC,OAAA,OAAAwC,EAAA,UAAAzC,EAAAC,QAAA,KAAAiC,KAAA,KAAAU,EAAAhI,EAAAiC,KAAAmD,EAAA,YAAA6C,EAAAjI,EAAAiC,KAAAmD,EAAA,iBAAA4C,GAAAC,EAAA,SAAAX,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,WAAAgC,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,WAAA,SAAAyC,GAAA,QAAAV,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,YAAA2C,EAAA,UAAA/D,MAAA,kDAAAoD,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,WAAA,KAAAb,OAAA,SAAArF,EAAA2C,GAAA,QAAAkE,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,QAAA,KAAAiC,MAAAtH,EAAAiC,KAAAmD,EAAA,oBAAAkC,KAAAlC,EAAAG,WAAA,KAAA2C,EAAA9C,EAAA,OAAA8C,IAAA,UAAA7I,GAAA,aAAAA,IAAA6I,EAAA7C,QAAArD,GAAAA,GAAAkG,EAAA3C,aAAA2C,EAAA,UAAAzE,EAAAyE,EAAAA,EAAAtC,WAAA,UAAAnC,EAAApE,KAAAA,EAAAoE,EAAAzB,IAAAA,EAAAkG,GAAA,KAAAhF,OAAA,YAAA8B,KAAAkD,EAAA3C,WAAAjD,GAAA,KAAA6F,SAAA1E,EAAA,EAAA0E,SAAA,SAAA1E,EAAA+B,GAAA,aAAA/B,EAAApE,KAAA,MAAAoE,EAAAzB,IAAA,gBAAAyB,EAAApE,MAAA,aAAAoE,EAAApE,KAAA,KAAA2F,KAAAvB,EAAAzB,IAAA,WAAAyB,EAAApE,MAAA,KAAAsI,KAAA,KAAA3F,IAAAyB,EAAAzB,IAAA,KAAAkB,OAAA,cAAA8B,KAAA,kBAAAvB,EAAApE,MAAAmG,IAAA,KAAAR,KAAAQ,GAAAlD,CAAA,EAAA8F,OAAA,SAAA7C,GAAA,QAAAW,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAG,aAAAA,EAAA,YAAA4C,SAAA/C,EAAAQ,WAAAR,EAAAI,UAAAG,EAAAP,GAAA9C,CAAA,GAAAuG,MAAA,SAAAxD,GAAA,QAAAa,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,SAAAA,EAAA,KAAA5B,EAAA2B,EAAAQ,WAAA,aAAAnC,EAAApE,KAAA,KAAAgJ,EAAA5E,EAAAzB,IAAA2D,EAAAP,EAAA,QAAAiD,CAAA,YAAAnE,MAAA,0BAAAoE,cAAA,SAAAxC,EAAAf,EAAAE,GAAA,YAAAb,SAAA,CAAA1D,SAAAoC,EAAAgD,GAAAf,WAAAA,EAAAE,QAAAA,GAAA,cAAA/B,SAAA,KAAAlB,SAAApC,GAAA0C,CAAA,GAAAzD,CAAA,UAAAiK,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA7I,EAAA4B,GAAA,QAAA8C,EAAAiE,EAAA3I,GAAA4B,GAAA1B,EAAAwE,EAAAxE,KAAA,OAAAwD,GAAA,YAAAN,EAAAM,EAAA,CAAAgB,EAAAH,KAAApB,EAAAjD,GAAAwG,QAAAvD,QAAAjD,GAAAsD,KAAAoF,EAAAC,EAAA,CAMAnK,EAAOD,QAAO,eANdkD,EAMc0H,GANd1H,EAMc4G,IAAAlC,MAAG,SAAAwD,EAAOH,EAAQ6W,GAAM,OAAAhY,IAAAxH,MAAA,SAAA+I,GAAA,cAAAA,EAAA5C,KAAA4C,EAAAlF,MAAA,OACpC8E,EAAO8W,YAAYD,GAAQ,wBAAAzW,EAAAzC,OAAA,GAAAwC,EAAA,IAP7B,eAAAhL,EAAA,KAAAkK,EAAAC,UAAA,WAAAtC,SAAA,SAAAvD,EAAAC,GAAA,IAAAuF,EAAAhH,EAAAsH,MAAApK,EAAAkK,GAAA,SAAAH,EAAA1I,GAAAwI,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA,OAAA3I,EAAA,UAAA2I,EAAA/H,GAAA4H,EAAAC,EAAAxF,EAAAC,EAAAwF,EAAAC,EAAA,QAAA/H,EAAA,CAAA8H,OAAApJ,EAAA,MAQC,gBAAAwK,EAAAC,GAAA,OAAAZ,EAAAJ,MAAA,KAAAD,UAAA,EAFa,YCAdtK,EAAOD,QAAU,SAAA4K,GAAmC,IAC9CK,EADckV,EAAUvV,EAAVuV,WAAY/S,EAAaxC,EAAbwC,cAE9B,GAAIsU,MAAQ3B,KAAO3S,EAAe,CAChC,IAAMiT,EAAO,IAAIqB,KAAK,CAAC,kBAADnM,OAAmB4K,EAAU,QAAQ,CACzD3f,KAAM,2BAERyK,EAAS,IAAI+W,OAAOjC,IAAIkC,gBAAgB5B,GAC1C,MACEpV,EAAS,IAAI+W,OAAO7B,GAGtB,OAAOlV,CACT,WCZAhL,EAAOD,QAAU,SAACiL,GAChBA,EAAOK,WACT,iDCRI4W,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBrhB,IAAjBshB,EACH,OAAOA,EAAariB,QAGrB,IAAIC,EAASiiB,EAAyBE,GAAY,CACjDrO,GAAIqO,EACJE,QAAQ,EACRtiB,QAAS,CAAC,GAUX,OANAuiB,EAAoBH,GAAUhf,KAAKnD,EAAOD,QAASC,EAAQA,EAAOD,QAASmiB,GAG3EliB,EAAOqiB,QAAS,EAGTriB,EAAOD,OACf,QCzBAmiB,EAAoBK,IAAOviB,IAC1BA,EAAOwiB,MAAQ,GACVxiB,EAAOyiB,WAAUziB,EAAOyiB,SAAW,IACjCziB,GCAkBkiB,EAAoB", "sources": ["webpack://Tesseract/webpack/universalModuleDefinition", "webpack://Tesseract/./node_modules/is-electron/index.js", "webpack://Tesseract/./node_modules/regenerator-runtime/runtime.js", "webpack://Tesseract/./src/Tesseract.js", "webpack://Tesseract/./src/constants/OEM.js", "webpack://Tesseract/./src/constants/PSM.js", "webpack://Tesseract/./src/constants/defaultOptions.js", "webpack://Tesseract/./src/constants/languages.js", "webpack://Tesseract/./src/createJob.js", "webpack://Tesseract/./src/createScheduler.js", "webpack://Tesseract/./src/createWorker.js", "webpack://Tesseract/./src/index.js", "webpack://Tesseract/./src/utils/circularize.js", "webpack://Tesseract/./src/utils/getEnvironment.js", "webpack://Tesseract/./src/utils/getId.js", "webpack://Tesseract/./src/utils/log.js", "webpack://Tesseract/./src/utils/resolvePaths.js", "webpack://Tesseract/./src/worker/browser/defaultOptions.js", "webpack://Tesseract/./src/worker/browser/index.js", "webpack://Tesseract/./src/worker/browser/loadImage.js", "webpack://Tesseract/./src/worker/browser/onMessage.js", "webpack://Tesseract/./src/worker/browser/send.js", "webpack://Tesseract/./src/worker/browser/spawnWorker.js", "webpack://Tesseract/./src/worker/browser/terminateWorker.js", "webpack://Tesseract/webpack/bootstrap", "webpack://Tesseract/webpack/runtime/node module decorator", "webpack://Tesseract/webpack/startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Tesseract\"] = factory();\n\telse\n\t\troot[\"Tesseract\"] = factory();\n})(self, () => {\nreturn ", "// https://github.com/electron/electron/issues/2288\nfunction isElectron() {\n    // Renderer process\n    if (typeof window !== 'undefined' && typeof window.process === 'object' && window.process.type === 'renderer') {\n        return true;\n    }\n\n    // Main process\n    if (typeof process !== 'undefined' && typeof process.versions === 'object' && !!process.versions.electron) {\n        return true;\n    }\n\n    // Detect the user agent when the `nodeIntegration` option is set to false\n    if (typeof navigator === 'object' && typeof navigator.userAgent === 'string' && navigator.userAgent.indexOf('Electron') >= 0) {\n        return true;\n    }\n\n    return false;\n}\n\nmodule.exports = isElectron;\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; };\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function define(obj, key, value) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n    return obj[key];\n  }\n  try {\n    // IE 8 has a broken Object.defineProperty that only works on DOM objects.\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    defineProperty(generator, \"_invoke\", { value: makeInvokeMethod(innerFn, self, context) });\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function () {\n    return this;\n  });\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = GeneratorFunctionPrototype;\n  defineProperty(Gp, \"constructor\", { value: GeneratorFunctionPrototype, configurable: true });\n  defineProperty(\n    GeneratorFunctionPrototype,\n    \"constructor\",\n    { value: GeneratorFunction, configurable: true }\n  );\n  GeneratorFunction.displayName = define(\n    GeneratorFunctionPrototype,\n    toStringTagSymbol,\n    \"GeneratorFunction\"\n  );\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      define(genFun, toStringTagSymbol, \"GeneratorFunction\");\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    defineProperty(this, \"_invoke\", { value: enqueue });\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  define(AsyncIterator.prototype, asyncIteratorSymbol, function () {\n    return this;\n  });\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var methodName = context.method;\n    var method = delegate.iterator[methodName];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method, or a missing .next mehtod, always terminate the\n      // yield* loop.\n      context.delegate = null;\n\n      // Note: [\"return\"] must be used for ES3 parsing compatibility.\n      if (methodName === \"throw\" && delegate.iterator[\"return\"]) {\n        // If the delegate iterator has a return method, give it a\n        // chance to clean up.\n        context.method = \"return\";\n        context.arg = undefined;\n        maybeInvokeDelegate(delegate, context);\n\n        if (context.method === \"throw\") {\n          // If maybeInvokeDelegate(context) changed context.method from\n          // \"return\" to \"throw\", let that override the TypeError below.\n          return ContinueSentinel;\n        }\n      }\n      if (methodName !== \"return\") {\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a '\" + methodName + \"' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  define(Gp, toStringTagSymbol, \"Generator\");\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  define(Gp, iteratorSymbol, function() {\n    return this;\n  });\n\n  define(Gp, \"toString\", function() {\n    return \"[object Generator]\";\n  });\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(val) {\n    var object = Object(val);\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, in modern engines\n  // we can explicitly access globalThis. In older engines we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n", "const createWorker = require('./createWorker');\n\nconst recognize = async (image, langs, options) => {\n  const worker = await createWorker(langs, 1, options);\n  return worker.recognize(image)\n    .finally(async () => {\n      await worker.terminate();\n    });\n};\n\nconst detect = async (image, options) => {\n  const worker = await createWorker('osd', 0, options);\n  return worker.detect(image)\n    .finally(async () => {\n      await worker.terminate();\n    });\n};\n\nmodule.exports = {\n  recognize,\n  detect,\n};\n", "/*\n * OEM = OCR Engine Mode, and there are 4 possible modes.\n *\n * By default tesseract.js uses LSTM_ONLY mode.\n *\n */\nmodule.exports = {\n  TESSERACT_ONLY: 0,\n  LSTM_ONLY: 1,\n  TESSERACT_LSTM_COMBINED: 2,\n  DEFAULT: 3,\n};\n", "/*\n * PSM = Page Segmentation Mode\n */\nmodule.exports = {\n  OSD_ONLY: '0',\n  AUTO_OSD: '1',\n  AUTO_ONLY: '2',\n  AUTO: '3',\n  SINGLE_COLUMN: '4',\n  SINGLE_BLOCK_VERT_TEXT: '5',\n  SINGLE_BLOCK: '6',\n  SINGLE_LINE: '7',\n  SINGLE_WORD: '8',\n  CIRCLE_WORD: '9',\n  SINGLE_CHAR: '10',\n  SPARSE_TEXT: '11',\n  SPARSE_TEXT_OSD: '12',\n  RAW_LINE: '13',\n};\n", "module.exports = {\n  /*\n   * Use BlobURL for worker script by default\n   * TODO: remove this option\n   *\n   */\n  workerBlobURL: true,\n  logger: () => {},\n};\n", "/*\n * languages with existing tesseract traineddata\n * https://tesseract-ocr.github.io/tessdoc/Data-Files#data-files-for-version-400-november-29-2016\n */\n\n/**\n * @typedef {object} Languages\n * @property {string} AFR Afrikaans\n * @property {string} AMH Amharic\n * @property {string} ARA Arabic\n * @property {string} ASM Assamese\n * @property {string} AZE Azerbaijani\n * @property {string} AZE_CYRL Azerbaijani - Cyrillic\n * @property {string} BEL Belarusian\n * @property {string} BEN Bengali\n * @property {string} BOD Tibetan\n * @property {string} BOS Bosnian\n * @property {string} BUL Bulgarian\n * @property {string} CAT Catalan; Valencian\n * @property {string} CEB Cebuano\n * @property {string} CES Czech\n * @property {string} CHI_SIM Chinese - Simplified\n * @property {string} CHI_TRA Chinese - Traditional\n * @property {string} CHR Cherokee\n * @property {string} CYM Welsh\n * @property {string} DAN Danish\n * @property {string} DEU German\n * @property {string} DZO Dzongkha\n * @property {string} ELL Greek, Modern (1453-)\n * @property {string} ENG English\n * @property {string} ENM English, Middle (1100-1500)\n * @property {string} EPO Esperanto\n * @property {string} EST Estonian\n * @property {string} EUS Basque\n * @property {string} FAS Persian\n * @property {string} FIN Finnish\n * @property {string} FRA French\n * @property {string} FRK German Fraktur\n * @property {string} FRM French, Middle (ca. 1400-1600)\n * @property {string} GLE Irish\n * @property {string} GLG Galician\n * @property {string} GRC Greek, Ancient (-1453)\n * @property {string} GUJ Gujarati\n * @property {string} HAT Haitian; Haitian Creole\n * @property {string} HEB Hebrew\n * @property {string} HIN Hindi\n * @property {string} HRV Croatian\n * @property {string} HUN Hungarian\n * @property {string} IKU Inuktitut\n * @property {string} IND Indonesian\n * @property {string} ISL Icelandic\n * @property {string} ITA Italian\n * @property {string} ITA_OLD Italian - Old\n * @property {string} JAV Javanese\n * @property {string} JPN Japanese\n * @property {string} KAN Kannada\n * @property {string} KAT Georgian\n * @property {string} KAT_OLD Georgian - Old\n * @property {string} KAZ Kazakh\n * @property {string} KHM Central Khmer\n * @property {string} KIR Kirghiz; Kyrgyz\n * @property {string} KOR Korean\n * @property {string} KUR Kurdish\n * @property {string} LAO Lao\n * @property {string} LAT Latin\n * @property {string} LAV Latvian\n * @property {string} LIT Lithuanian\n * @property {string} MAL Malayalam\n * @property {string} MAR Marathi\n * @property {string} MKD Macedonian\n * @property {string} MLT Maltese\n * @property {string} MSA Malay\n * @property {string} MYA Burmese\n * @property {string} NEP Nepali\n * @property {string} NLD Dutch; Flemish\n * @property {string} NOR Norwegian\n * @property {string} ORI Oriya\n * @property {string} PAN Panjabi; Punjabi\n * @property {string} POL Polish\n * @property {string} POR Portuguese\n * @property {string} PUS Pushto; Pashto\n * @property {string} RON Romanian; Moldavian; Moldovan\n * @property {string} RUS Russian\n * @property {string} SAN Sanskrit\n * @property {string} SIN Sinhala; Sinhalese\n * @property {string} SLK Slovak\n * @property {string} SLV Slovenian\n * @property {string} SPA Spanish; Castilian\n * @property {string} SPA_OLD Spanish; Castilian - Old\n * @property {string} SQI Albanian\n * @property {string} SRP Serbian\n * @property {string} SRP_LATN Serbian - Latin\n * @property {string} SWA Swahili\n * @property {string} SWE Swedish\n * @property {string} SYR Syriac\n * @property {string} TAM Tamil\n * @property {string} TEL Telugu\n * @property {string} TGK Tajik\n * @property {string} TGL Tagalog\n * @property {string} THA Thai\n * @property {string} TIR Tigrinya\n * @property {string} TUR Turkish\n * @property {string} UIG Uighur; Uyghur\n * @property {string} UKR Ukrainian\n * @property {string} URD Urdu\n * @property {string} UZB Uzbek\n * @property {string} UZB_CYRL Uzbek - Cyrillic\n * @property {string} VIE Vietnamese\n * @property {string} YID Yiddish\n */\n\n/**\n  * @type {Languages}\n  */\nmodule.exports = {\n  AFR: 'afr',\n  AMH: 'amh',\n  ARA: 'ara',\n  ASM: 'asm',\n  AZE: 'aze',\n  AZE_CYRL: 'aze_cyrl',\n  BEL: 'bel',\n  BEN: 'ben',\n  BOD: 'bod',\n  BOS: 'bos',\n  BUL: 'bul',\n  CAT: 'cat',\n  CEB: 'ceb',\n  CES: 'ces',\n  CHI_SIM: 'chi_sim',\n  CHI_TRA: 'chi_tra',\n  CHR: 'chr',\n  CYM: 'cym',\n  DAN: 'dan',\n  DEU: 'deu',\n  DZO: 'dzo',\n  ELL: 'ell',\n  ENG: 'eng',\n  ENM: 'enm',\n  EPO: 'epo',\n  EST: 'est',\n  EUS: 'eus',\n  FAS: 'fas',\n  FIN: 'fin',\n  FRA: 'fra',\n  FRK: 'frk',\n  FRM: 'frm',\n  GLE: 'gle',\n  GLG: 'glg',\n  GRC: 'grc',\n  GUJ: 'guj',\n  HAT: 'hat',\n  HEB: 'heb',\n  HIN: 'hin',\n  HRV: 'hrv',\n  HUN: 'hun',\n  IKU: 'iku',\n  IND: 'ind',\n  ISL: 'isl',\n  ITA: 'ita',\n  ITA_OLD: 'ita_old',\n  JAV: 'jav',\n  JPN: 'jpn',\n  KAN: 'kan',\n  KAT: 'kat',\n  KAT_OLD: 'kat_old',\n  KAZ: 'kaz',\n  KHM: 'khm',\n  KIR: 'kir',\n  KOR: 'kor',\n  KUR: 'kur',\n  LAO: 'lao',\n  LAT: 'lat',\n  LAV: 'lav',\n  LIT: 'lit',\n  MAL: 'mal',\n  MAR: 'mar',\n  MKD: 'mkd',\n  MLT: 'mlt',\n  MSA: 'msa',\n  MYA: 'mya',\n  NEP: 'nep',\n  NLD: 'nld',\n  NOR: 'nor',\n  ORI: 'ori',\n  PAN: 'pan',\n  POL: 'pol',\n  POR: 'por',\n  PUS: 'pus',\n  RON: 'ron',\n  RUS: 'rus',\n  SAN: 'san',\n  SIN: 'sin',\n  SLK: 'slk',\n  SLV: 'slv',\n  SPA: 'spa',\n  SPA_OLD: 'spa_old',\n  SQI: 'sqi',\n  SRP: 'srp',\n  SRP_LATN: 'srp_latn',\n  SWA: 'swa',\n  SWE: 'swe',\n  SYR: 'syr',\n  TAM: 'tam',\n  TEL: 'tel',\n  TGK: 'tgk',\n  TGL: 'tgl',\n  THA: 'tha',\n  TIR: 'tir',\n  TUR: 'tur',\n  UIG: 'uig',\n  UKR: 'ukr',\n  URD: 'urd',\n  UZB: 'uzb',\n  UZB_CYRL: 'uzb_cyrl',\n  VIE: 'vie',\n  YID: 'yid',\n};\n", "const getId = require('./utils/getId');\n\nlet jobCounter = 0;\n\nmodule.exports = ({\n  id: _id,\n  action,\n  payload = {},\n}) => {\n  let id = _id;\n  if (typeof id === 'undefined') {\n    id = getId('Job', jobCounter);\n    jobCounter += 1;\n  }\n\n  return {\n    id,\n    action,\n    payload,\n  };\n};\n", "const createJob = require('./createJob');\nconst { log } = require('./utils/log');\nconst getId = require('./utils/getId');\n\nlet schedulerCounter = 0;\n\nmodule.exports = () => {\n  const id = getId('Scheduler', schedulerCounter);\n  const workers = {};\n  const runningWorkers = {};\n  let jobQueue = [];\n\n  schedulerCounter += 1;\n\n  const getQueueLen = () => jobQueue.length;\n  const getNumWorkers = () => Object.keys(workers).length;\n\n  const dequeue = () => {\n    if (jobQueue.length !== 0) {\n      const wIds = Object.keys(workers);\n      for (let i = 0; i < wIds.length; i += 1) {\n        if (typeof runningWorkers[wIds[i]] === 'undefined') {\n          jobQueue[0](workers[wIds[i]]);\n          break;\n        }\n      }\n    }\n  };\n\n  const queue = (action, payload) => (\n    new Promise((resolve, reject) => {\n      const job = createJob({ action, payload });\n      jobQueue.push(async (w) => {\n        jobQueue.shift();\n        runningWorkers[w.id] = job;\n        try {\n          resolve(await w[action].apply(this, [...payload, job.id]));\n        } catch (err) {\n          reject(err);\n        } finally {\n          delete runningWorkers[w.id];\n          dequeue();\n        }\n      });\n      log(`[${id}]: Add ${job.id} to JobQueue`);\n      log(`[${id}]: JobQueue length=${jobQueue.length}`);\n      dequeue();\n    })\n  );\n\n  const addWorker = (w) => {\n    workers[w.id] = w;\n    log(`[${id}]: Add ${w.id}`);\n    log(`[${id}]: Number of workers=${getNumWorkers()}`);\n    dequeue();\n    return w.id;\n  };\n\n  const addJob = async (action, ...payload) => {\n    if (getNumWorkers() === 0) {\n      throw Error(`[${id}]: You need to have at least one worker before adding jobs`);\n    }\n    return queue(action, payload);\n  };\n\n  const terminate = async () => {\n    Object.keys(workers).forEach(async (wid) => {\n      await workers[wid].terminate();\n    });\n    jobQueue = [];\n  };\n\n  return {\n    addWorker,\n    addJob,\n    terminate,\n    getQueueLen,\n    getNumWorkers,\n  };\n};\n", "const resolvePaths = require('./utils/resolvePaths');\nconst circularize = require('./utils/circularize');\nconst createJob = require('./createJob');\nconst { log } = require('./utils/log');\nconst getId = require('./utils/getId');\nconst OEM = require('./constants/OEM');\nconst {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  loadImage,\n  send,\n} = require('./worker/node');\n\nlet workerCounter = 0;\n\nmodule.exports = async (langs = 'eng', oem = OEM.LSTM_ONLY, _options = {}, config = {}) => {\n  const id = getId('Worker', workerCounter);\n  const {\n    logger,\n    errorHandler,\n    ...options\n  } = resolvePaths({\n    ...defaultOptions,\n    ..._options,\n  });\n  const resolves = {};\n  const rejects = {};\n\n  // Current langs, oem, and config file.\n  // Used if the user ever re-initializes the worker using `worker.reinitialize`.\n  const currentLangs = typeof langs === 'string' ? langs.split('+') : langs;\n  let currentOem = oem;\n  let currentConfig = config;\n  const lstmOnlyCore = [OEM.DEFAULT, OEM.LSTM_ONLY].includes(oem) && !options.legacyCore;\n\n  let workerResReject;\n  let workerResResolve;\n  const workerRes = new Promise((resolve, reject) => {\n    workerResResolve = resolve;\n    workerResReject = reject;\n  });\n  const workerError = (event) => { workerResReject(event.message); };\n\n  let worker = spawnWorker(options);\n  worker.onerror = workerError;\n\n  workerCounter += 1;\n\n  const setResolve = (promiseId, res) => {\n    resolves[promiseId] = res;\n  };\n\n  const setReject = (promiseId, rej) => {\n    rejects[promiseId] = rej;\n  };\n\n  const startJob = ({ id: jobId, action, payload }) => (\n    new Promise((resolve, reject) => {\n      log(`[${id}]: Start ${jobId}, action=${action}`);\n      // Using both `action` and `jobId` in case user provides non-unique `jobId`.\n      const promiseId = `${action}-${jobId}`;\n      setResolve(promiseId, resolve);\n      setReject(promiseId, reject);\n      send(worker, {\n        workerId: id,\n        jobId,\n        action,\n        payload,\n      });\n    })\n  );\n\n  const load = () => (\n    console.warn('`load` is depreciated and should be removed from code (workers now come pre-loaded)')\n  );\n\n  const loadInternal = (jobId) => (\n    startJob(createJob({\n      id: jobId, action: 'load', payload: { options: { lstmOnly: lstmOnlyCore, corePath: options.corePath, logging: options.logging } },\n    }))\n  );\n\n  const writeText = (path, text, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'writeFile', args: [path, text] },\n    }))\n  );\n\n  const readText = (path, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'readFile', args: [path, { encoding: 'utf8' }] },\n    }))\n  );\n\n  const removeFile = (path, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'unlink', args: [path] },\n    }))\n  );\n\n  const FS = (method, args, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method, args },\n    }))\n  );\n\n  const loadLanguage = () => (\n    console.warn('`loadLanguage` is depreciated and should be removed from code (workers now come with language pre-loaded)')\n  );\n\n  const loadLanguageInternal = (_langs, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'loadLanguage',\n    payload: {\n      langs: _langs,\n      options: {\n        langPath: options.langPath,\n        dataPath: options.dataPath,\n        cachePath: options.cachePath,\n        cacheMethod: options.cacheMethod,\n        gzip: options.gzip,\n        lstmOnly: [OEM.DEFAULT, OEM.LSTM_ONLY].includes(currentOem)\n          && !options.legacyLang,\n      },\n    },\n  }));\n\n  const initialize = () => (\n    console.warn('`initialize` is depreciated and should be removed from code (workers now come pre-initialized)')\n  );\n\n  const initializeInternal = (_langs, _oem, _config, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'initialize',\n      payload: { langs: _langs, oem: _oem, config: _config },\n    }))\n  );\n\n  const reinitialize = (langs = 'eng', oem, config, jobId) => { // eslint-disable-line\n\n    if (lstmOnlyCore && [OEM.TESSERACT_ONLY, OEM.TESSERACT_LSTM_COMBINED].includes(oem)) throw Error('Legacy model requested but code missing.');\n\n    const _oem = oem || currentOem;\n    currentOem = _oem;\n\n    const _config = config || currentConfig;\n    currentConfig = _config;\n\n    // Only load langs that are not already loaded.\n    // This logic fails if the user downloaded the LSTM-only English data for a language\n    // and then uses `worker.reinitialize` to switch to the Legacy engine.\n    // However, the correct data will still be downloaded after initialization fails\n    // and this can be avoided entirely if the user loads the correct data ahead of time.\n    const langsArr = typeof langs === 'string' ? langs.split('+') : langs;\n    const _langs = langsArr.filter((x) => !currentLangs.includes(x));\n    currentLangs.push(..._langs);\n\n    if (_langs.length > 0) {\n      return loadLanguageInternal(_langs, jobId)\n        .then(() => initializeInternal(langs, _oem, _config, jobId));\n    }\n\n    return initializeInternal(langs, _oem, _config, jobId);\n  };\n\n  const setParameters = (params = {}, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'setParameters',\n      payload: { params },\n    }))\n  );\n\n  const recognize = async (image, opts = {}, output = {\n    blocks: true, text: true, hocr: true, tsv: true,\n  }, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'recognize',\n      payload: { image: await loadImage(image), options: opts, output },\n    }))\n  );\n\n  const getPDF = (title = 'Tesseract OCR Result', textonly = false, jobId) => {\n    console.log('`getPDF` function is depreciated. `recognize` option `savePDF` should be used instead.');\n    return startJob(createJob({\n      id: jobId,\n      action: 'getPDF',\n      payload: { title, textonly },\n    }));\n  };\n\n  const detect = async (image, jobId) => {\n    if (lstmOnlyCore) throw Error('`worker.detect` requires Legacy model, which was not loaded.');\n\n    return startJob(createJob({\n      id: jobId,\n      action: 'detect',\n      payload: { image: await loadImage(image) },\n    }));\n  };\n\n  const terminate = async () => {\n    if (worker !== null) {\n      /*\n      await startJob(createJob({\n        id: jobId,\n        action: 'terminate',\n      }));\n      */\n      terminateWorker(worker);\n      worker = null;\n    }\n    return Promise.resolve();\n  };\n\n  onMessage(worker, ({\n    workerId, jobId, status, action, data,\n  }) => {\n    const promiseId = `${action}-${jobId}`;\n    if (status === 'resolve') {\n      log(`[${workerId}]: Complete ${jobId}`);\n      let d = data;\n      if (action === 'recognize') {\n        d = circularize(data);\n      } else if (action === 'getPDF') {\n        d = Array.from({ ...data, length: Object.keys(data).length });\n      }\n      resolves[promiseId]({ jobId, data: d });\n    } else if (status === 'reject') {\n      rejects[promiseId](data);\n      if (action === 'load') workerResReject(data);\n      if (errorHandler) {\n        errorHandler(data);\n      } else {\n        throw Error(data);\n      }\n    } else if (status === 'progress') {\n      logger({ ...data, userJobId: jobId });\n    }\n  });\n\n  const resolveObj = {\n    id,\n    worker,\n    setResolve,\n    setReject,\n    load,\n    writeText,\n    readText,\n    removeFile,\n    FS,\n    loadLanguage,\n    initialize,\n    reinitialize,\n    setParameters,\n    recognize,\n    getPDF,\n    detect,\n    terminate,\n  };\n\n  loadInternal()\n    .then(() => loadLanguageInternal(langs))\n    .then(() => initializeInternal(langs, oem, config))\n    .then(() => workerResResolve(resolveObj))\n    .catch(() => {});\n\n  return workerRes;\n};\n", "/**\n *\n * Entry point for tesseract.js, should be the entry when bundling.\n *\n * @fileoverview entry point for tesseract.js\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nrequire('regenerator-runtime/runtime');\nconst createScheduler = require('./createScheduler');\nconst createWorker = require('./createWorker');\nconst Tesseract = require('./Tesseract');\nconst languages = require('./constants/languages');\nconst OEM = require('./constants/OEM');\nconst PSM = require('./constants/PSM');\nconst { setLogging } = require('./utils/log');\n\nmodule.exports = {\n  languages,\n  OEM,\n  PSM,\n  createScheduler,\n  createWorker,\n  setLogging,\n  ...Tesseract,\n};\n", "/**\n * In the recognition result of tesseract, there\n * is a deep JSON object for details, it has around\n *\n * The result of dump.js is a big JSON tree\n * which can be easily serialized (for instance\n * to be sent from a webworker to the main app\n * or through Node's IPC), but we want\n * a (circular) DOM-like interface for walking\n * through the data.\n *\n * @fileoverview DOM-like interface for walking through data\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <j<PERSON><PERSON><EMAIL>>\n */\n\nmodule.exports = (page) => {\n  const blocks = [];\n  const paragraphs = [];\n  const lines = [];\n  const words = [];\n  const symbols = [];\n\n  if (page.blocks) {\n    page.blocks.forEach((block) => {\n      block.paragraphs.forEach((paragraph) => {\n        paragraph.lines.forEach((line) => {\n          line.words.forEach((word) => {\n            word.symbols.forEach((sym) => {\n              symbols.push({\n                ...sym, page, block, paragraph, line, word,\n              });\n            });\n            words.push({\n              ...word, page, block, paragraph, line,\n            });\n          });\n          lines.push({\n            ...line, page, block, paragraph,\n          });\n        });\n        paragraphs.push({\n          ...paragraph, page, block,\n        });\n      });\n      blocks.push({\n        ...block, page,\n      });\n    });\n  }\n\n  return {\n    ...page, blocks, paragraphs, lines, words, symbols,\n  };\n};\n", "const isElectron = require('is-electron');\n\nmodule.exports = (key) => {\n  const env = {};\n\n  if (typeof WorkerGlobalScope !== 'undefined') {\n    env.type = 'webworker';\n  } else if (isElectron()) {\n    env.type = 'electron';\n  } else if (typeof document === 'object') {\n    env.type = 'browser';\n  } else if (typeof process === 'object' && typeof require === 'function') {\n    env.type = 'node';\n  }\n\n  if (typeof key === 'undefined') {\n    return env;\n  }\n\n  return env[key];\n};\n", "module.exports = (prefix, cnt) => (\n  `${prefix}-${cnt}-${Math.random().toString(16).slice(3, 8)}`\n);\n", "let logging = false;\n\nexports.logging = logging;\n\nexports.setLogging = (_logging) => {\n  logging = _logging;\n};\n\nexports.log = (...args) => (logging ? console.log.apply(this, args) : null);\n", "const isBrowser = require('./getEnvironment')('type') === 'browser';\n\nconst resolveURL = isBrowser ? s => (new URL(s, window.location.href)).href : s => s; // eslint-disable-line\n\nmodule.exports = (options) => {\n  const opts = { ...options };\n  ['corePath', 'workerPath', 'langPath'].forEach((key) => {\n    if (options[key]) {\n      opts[key] = resolveURL(opts[key]);\n    }\n  });\n  return opts;\n};\n", "const version = require('../../../package.json').version;\nconst defaultOptions = require('../../constants/defaultOptions');\n\n/*\n * Default options for browser worker\n */\nmodule.exports = {\n  ...defaultOptions,\n  workerPath: `https://cdn.jsdelivr.net/npm/tesseract.js@v${version}/dist/worker.min.js`,\n};\n", "/**\n *\n * Tesseract Worker adapter for browser\n *\n * @fileoverview Tesseract Worker adapter for browser\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nconst defaultOptions = require('./defaultOptions');\nconst spawnWorker = require('./spawnWorker');\nconst terminateWorker = require('./terminateWorker');\nconst onMessage = require('./onMessage');\nconst send = require('./send');\nconst loadImage = require('./loadImage');\n\nmodule.exports = {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  send,\n  loadImage,\n};\n", "/**\n * readFromBlobOrFile\n *\n * @name readFromBlobOrFile\n * @function\n * @access private\n */\nconst readFromBlobOrFile = (blob) => (\n  new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = () => {\n      resolve(fileReader.result);\n    };\n    fileReader.onerror = ({ target: { error: { code } } }) => {\n      reject(Error(`File could not be read! Code=${code}`));\n    };\n    fileReader.readAsArrayBuffer(blob);\n  })\n);\n\n/**\n * loadImage\n *\n * @name loadImage\n * @function load image from different source\n * @access private\n */\nconst loadImage = async (image) => {\n  let data = image;\n  if (typeof image === 'undefined') {\n    return 'undefined';\n  }\n\n  if (typeof image === 'string') {\n    // Base64 Image\n    if (/data:image\\/([a-zA-Z]*);base64,([^\"]*)/.test(image)) {\n      data = atob(image.split(',')[1])\n        .split('')\n        .map((c) => c.charCodeAt(0));\n    } else {\n      const resp = await fetch(image);\n      data = await resp.arrayBuffer();\n    }\n  } else if (typeof HTMLElement !== 'undefined' && image instanceof HTMLElement) {\n    if (image.tagName === 'IMG') {\n      data = await loadImage(image.src);\n    }\n    if (image.tagName === 'VIDEO') {\n      data = await loadImage(image.poster);\n    }\n    if (image.tagName === 'CANVAS') {\n      await new Promise((resolve) => {\n        image.toBlob(async (blob) => {\n          data = await readFromBlobOrFile(blob);\n          resolve();\n        });\n      });\n    }\n  } else if (typeof OffscreenCanvas !== 'undefined' && image instanceof OffscreenCanvas) {\n    const blob = await image.convertToBlob();\n    data = await readFromBlobOrFile(blob);\n  } else if (image instanceof File || image instanceof Blob) {\n    data = await readFromBlobOrFile(image);\n  }\n\n  return new Uint8Array(data);\n};\n\nmodule.exports = loadImage;\n", "module.exports = (worker, handler) => {\n  worker.onmessage = ({ data }) => { // eslint-disable-line\n    handler(data);\n  };\n};\n", "/**\n * send\n *\n * @name send\n * @function send packet to worker and create a job\n * @access public\n */\nmodule.exports = async (worker, packet) => {\n  worker.postMessage(packet);\n};\n", "/**\n * spawnWorker\n *\n * @name spawnWorker\n * @function create a new Worker in browser\n * @access public\n */\nmodule.exports = ({ workerPath, workerBlobURL }) => {\n  let worker;\n  if (Blob && URL && workerBlobURL) {\n    const blob = new Blob([`importScripts(\"${workerPath}\");`], {\n      type: 'application/javascript',\n    });\n    worker = new Worker(URL.createObjectURL(blob));\n  } else {\n    worker = new Worker(workerPath);\n  }\n\n  return worker;\n};\n", "/**\n * terminateWorker\n *\n * @name terminateWorker\n * @function terminate worker\n * @access public\n */\nmodule.exports = (worker) => {\n  worker.terminate();\n};\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(352);\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "window", "_typeof", "process", "type", "versions", "electron", "navigator", "userAgent", "indexOf", "runtime", "undefined", "Op", "Object", "prototype", "hasOwn", "hasOwnProperty", "defineProperty", "obj", "key", "desc", "value", "$Symbol", "Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "toStringTag", "enumerable", "configurable", "writable", "err", "wrap", "innerFn", "outerFn", "tryLocsList", "protoGenerator", "Generator", "generator", "create", "context", "Context", "makeInvokeMethod", "tryCatch", "fn", "arg", "call", "GenStateSuspendedStart", "GenStateSuspendedYield", "GenStateExecuting", "GenStateCompleted", "ContinueSentinel", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "this", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "for<PERSON>ach", "method", "_invoke", "AsyncIterator", "PromiseImpl", "invoke", "resolve", "reject", "record", "result", "__await", "then", "unwrapped", "error", "previousPromise", "callInvokeWithMethodAndArg", "state", "Error", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "done", "methodName", "TypeError", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "length", "i", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "iter", "keys", "val", "object", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "slice", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "thrown", "<PERSON><PERSON><PERSON>", "regeneratorRuntime", "accidentalStrictMode", "globalThis", "Function", "_regeneratorRuntime", "return", "catch", "asyncGeneratorStep", "gen", "_next", "_throw", "_asyncToGenerator", "args", "arguments", "apply", "createWorker", "require", "recognize", "_ref", "_callee2", "image", "langs", "options", "worker", "_context2", "finally", "_callee", "_context", "terminate", "_x", "_x2", "_x3", "detect", "_ref3", "_callee4", "_context4", "_callee3", "_context3", "_x4", "_x5", "TESSERACT_ONLY", "LSTM_ONLY", "TESSERACT_LSTM_COMBINED", "DEFAULT", "OSD_ONLY", "AUTO_OSD", "AUTO_ONLY", "AUTO", "SINGLE_COLUMN", "SINGLE_BLOCK_VERT_TEXT", "SINGLE_BLOCK", "SINGLE_LINE", "SINGLE_WORD", "CIRCLE_WORD", "SINGLE_CHAR", "SPARSE_TEXT", "SPARSE_TEXT_OSD", "RAW_LINE", "workerBlobURL", "logger", "AFR", "AMH", "ARA", "ASM", "AZE", "AZE_CYRL", "BEL", "BEN", "BOD", "BOS", "BUL", "CAT", "CEB", "CES", "CHI_SIM", "CHI_TRA", "CHR", "CYM", "DAN", "DEU", "DZO", "ELL", "ENG", "ENM", "EPO", "EST", "EUS", "FAS", "FIN", "FRA", "FRK", "FRM", "GLE", "GLG", "GRC", "GUJ", "HAT", "HEB", "HIN", "HRV", "HUN", "IKU", "IND", "ISL", "ITA", "ITA_OLD", "JAV", "JPN", "KAN", "KAT", "KAT_OLD", "KAZ", "KHM", "KIR", "KOR", "KUR", "LAO", "LAT", "LAV", "LIT", "MAL", "MAR", "MKD", "MLT", "MSA", "MYA", "NEP", "NLD", "NOR", "ORI", "PAN", "POL", "POR", "PUS", "RON", "RUS", "SAN", "SIN", "SLK", "SLV", "SPA", "SPA_OLD", "SQI", "SRP", "SRP_LATN", "SWA", "SWE", "SYR", "TAM", "TEL", "TGK", "TGL", "THA", "TIR", "TUR", "UIG", "UKR", "URD", "UZB", "UZB_CYRL", "VIE", "YID", "getId", "jobCounter", "_id", "id", "action", "_ref$payload", "payload", "_arrayLikeToArray", "arr", "len", "arr2", "Array", "createJob", "log", "schedulerCounter", "workers", "runningWorkers", "jobQueue", "getNumWorkers", "dequeue", "wIds", "queue", "job", "w", "shift", "t0", "_this", "concat", "isArray", "_arrayWithoutHoles", "from", "_iterableToArray", "o", "minLen", "n", "toString", "test", "_unsupportedIterableToArray", "_nonIterableSpread", "t1", "t2", "addJob", "_ref2", "_len", "_key", "_args2", "_ref4", "wid", "addWorker", "getQueueLen", "ownKeys", "enumerableOnly", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "_objectSpread", "target", "source", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "input", "hint", "prim", "toPrimitive", "res", "String", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectWithoutProperties", "excluded", "sourceKeys", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "propertyIsEnumerable", "resolvePaths", "circularize", "OEM", "_require2", "defaultOptions", "spawnWorker", "terminateWorker", "onMessage", "loadImage", "send", "workerCounter", "oem", "_options", "config", "_resolvePaths", "<PERSON><PERSON><PERSON><PERSON>", "resolves", "rejects", "current<PERSON><PERSON><PERSON>", "currentOem", "currentConfig", "lstmOnlyCore", "workerResReject", "workerResResolve", "workerRes", "workerError", "setResolve", "setReject", "startJob", "load", "loadInternal", "writeText", "readText", "removeFile", "FS", "loadLanguage", "loadLanguageInternal", "initialize", "initializeInternal", "reinitialize", "setParameters", "getPDF", "resolveObj", "_args4", "_excluded", "split", "includes", "legacyCore", "event", "message", "onerror", "promiseId", "rej", "jobId", "workerId", "console", "warn", "lstmOnly", "corePath", "logging", "path", "text", "encoding", "_langs", "lang<PERSON><PERSON>", "dataPath", "cachePath", "cacheMethod", "gzip", "legacyLang", "_oem", "_config", "x", "params", "opts", "output", "_args", "blocks", "hocr", "tsv", "t3", "t4", "t5", "t6", "t7", "t8", "title", "textonly", "_ref5", "_ref6", "status", "data", "d", "userJobId", "createScheduler", "Tesseract", "languages", "PSM", "setLogging", "page", "paragraphs", "lines", "words", "block", "paragraph", "line", "word", "isElectron", "env", "WorkerGlobalScope", "document", "prefix", "cnt", "Math", "random", "_logging", "resolveURL", "s", "URL", "location", "href", "version", "worker<PERSON><PERSON>", "readFromBlobOrFile", "blob", "fileReader", "FileReader", "onload", "code", "readAsA<PERSON>y<PERSON><PERSON>er", "resp", "atob", "map", "c", "charCodeAt", "fetch", "arrayBuffer", "HTMLElement", "tagName", "src", "poster", "toBlob", "OffscreenCanvas", "convertToBlob", "File", "Blob", "Uint8Array", "handler", "onmessage", "packet", "postMessage", "Worker", "createObjectURL", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "loaded", "__webpack_modules__", "nmd", "paths", "children"], "sourceRoot": ""}