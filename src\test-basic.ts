import { parse_input } from './agent';
import * as fs from 'fs';
import * as path from 'path';

console.log('🔬 Basic CAPTCHA Agent Test\n');

async function testBasicFunctionality() {
  try {
    console.log('📝 Testing input validation...');
    
    // Test valid input
    const validInput = { image_base64: 'dGVzdA==' }; // "test" in base64
    const parsed = parse_input(validInput);
    console.log('✅ Valid input parsing successful');
    
    // Test invalid input
    try {
      parse_input({ invalid: 'data' });
      console.log('❌ Invalid input test failed');
    } catch (error) {
      console.log('✅ Invalid input properly rejected');
    }
    
    console.log('\n📁 Checking test image...');
    const testImagePath = path.join(__dirname, '../test-images/captcha-test.png');
    
    if (fs.existsSync(testImagePath)) {
      const imageBuffer = fs.readFileSync(testImagePath);
      const base64Image = imageBuffer.toString('base64');
      console.log('✅ Test image loaded successfully');
      console.log(`📊 Image size: ${imageBuffer.length} bytes`);
      console.log(`📊 Base64 length: ${base64Image.length} characters`);
      
      // Test parsing with real image
      const realInput = parse_input({ image_base64: base64Image });
      console.log('✅ Real image input parsing successful');
      
    } else {
      console.log('❌ Test image not found');
    }
    
    console.log('\n🎉 Basic functionality test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during basic test:', error);
  }
}

testBasicFunctionality();
