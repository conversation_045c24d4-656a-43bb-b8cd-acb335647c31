{"name": "is-electron", "version": "2.2.2", "description": "Detect if running in Electron.", "homepage": "https://github.com/cheton/is-electron", "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts"], "scripts": {"test": "tap --coverage test/*.js", "coveralls": "tap --coverage --coverage-report=text-lcov test/*.js | node_modules/.bin/coveralls"}, "author": "Cheton Wu <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "**************:cheton/is-electron.git"}, "keywords": ["atom", "electron", "renderer", "process"], "devDependencies": {"coveralls": "^2.11.9", "tap": "^5.7.2"}, "nyc": {"exclude": []}}