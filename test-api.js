const fs = require('fs');
const path = require('path');

async function testCaptchaAPI() {
  console.log('🚀 Testing CAPTCHA API...\n');
  
  try {
    // Read the test image
    const testImagePath = path.join(__dirname, 'test-images', 'captcha-test.png');
    
    if (!fs.existsSync(testImagePath)) {
      console.error('❌ Test image not found at:', testImagePath);
      return;
    }
    
    const imageBuffer = fs.readFileSync(testImagePath);
    const base64Image = imageBuffer.toString('base64');
    
    console.log('📁 Test image loaded');
    console.log(`📊 Image size: ${imageBuffer.length} bytes`);
    
    // Prepare the request
    const requestData = {
      image_base64: base64Image
    };
    
    console.log('\n🔄 Sending request to API...');
    const startTime = Date.now();
    
    // Use fetch to send the request
    const response = await fetch('http://localhost:3000/api/solve', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`⏱️  Request completed in ${duration}ms`);
    
    if (!response.ok) {
      console.error('❌ HTTP Error:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Error details:', errorText);
      return;
    }
    
    const result = await response.json();
    
    console.log('\n🎉 Response received:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ CAPTCHA solved successfully!');
      console.log(`📍 Result: ${result.result}`);
      if (result.coordinates && result.coordinates.length > 0) {
        console.log(`📐 Coordinates: ${JSON.stringify(result.coordinates)}`);
        console.log(`🎯 Found ${result.coordinates.length} matching cell(s)`);
      }
    } else {
      console.log('\n❌ CAPTCHA solving failed');
      console.log(`Error: ${result.error}`);
    }
    
  } catch (error) {
    console.error('\n💥 Error during test:', error.message);
  }
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('Installing node-fetch for older Node.js versions...');
  try {
    const fetch = require('node-fetch');
    global.fetch = fetch;
  } catch (e) {
    console.error('❌ fetch not available. Please install node-fetch or use Node.js 18+');
    process.exit(1);
  }
}

testCaptchaAPI();
