# PowerShell test for CAPTCHA API
Write-Host "Testing CAPTCHA API with PowerShell..." -ForegroundColor Green

try {
    # Test with simple base64 data first
    $testData = @{
        image_base64 = "dGVzdA=="
    } | ConvertTo-Json

    Write-Host "Sending test request..." -ForegroundColor Yellow

    $response = Invoke-RestMethod -Uri "http://localhost:3000/api/solve" -Method POST -Body $testData -ContentType "application/json"

    Write-Host "Response received:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 3 | Write-Host

}
catch {
    Write-Host "Error occurred:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}
