import { OpenAI } from 'openai';
import dotenv from 'dotenv';
import sharp from 'sharp';
import Tesseract from 'tesseract.js';

// Charger les variables d'environnement
dotenv.config();

// Initialiser le client OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Types pour notre agent
export interface CaptchaInput {
  image_base64: string;
}

export interface CaptchaOutput {
  success: boolean;
  result: string;
  coordinates?: Array<[number, number]>;
  timestamp: string;
  error?: string;
}

// Types pour la grille
interface CellPosition {
  row: number;
  col: number;
  label: string;
}

interface GridAnalysisResult {
  targetNumber: string | null;
  cells: CellPosition[];
  matchingCells: CellPosition[];
  coordinates: Array<[number, number]>;
}

/**
 * Analyse et valide l'entrée reçue
 * @param input - Données brutes reçues par l'API
 * @returns Entrée validée
 */
export function parse_input(input: any): CaptchaInput {
  // Vérifier que l'entrée est un objet
  if (typeof input !== 'object' || input === null) {
    throw new Error('L\'entrée doit être un objet');
  }

  // Vérifier que l'image est présente et en base64
  if (!input.image_base64 || typeof input.image_base64 !== 'string') {
    throw new Error('L\'image en base64 est requise');
  }

  // Vérifier que le string est bien en base64
  try {
    // Vérification simple du format base64
    if (!input.image_base64.match(/^[A-Za-z0-9+/=]+$/)) {
      throw new Error('L\'image n\'est pas correctement encodée en base64');
    }
  } catch (error) {
    throw new Error('L\'image n\'est pas correctement encodée en base64');
  }

  return {
    image_base64: input.image_base64
  };
}

/**
 * Convertit une image base64 en buffer
 * @param base64 - Image encodée en base64
 * @returns Buffer de l'image
 */
function base64ToBuffer(base64: string): Buffer {
  return Buffer.from(base64, 'base64');
}

/**
 * Utilise OpenAI pour extraire l'instruction du CAPTCHA
 * @param base64Image - Image encodée en base64
 * @returns L'instruction extraite (ex: "Please select all boxes with number 905")
 */
async function extractInstructionWithOpenAI(base64Image: string): Promise<string> {
  try {
    const prompt = "Cette image contient un CAPTCHA avec une instruction en haut. Extrais et retourne uniquement l'instruction textuelle (ex: 'Please select all boxes with number 905'). Ne retourne que le texte exact de l'instruction, rien d'autre.";

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "Tu es un agent spécialisé dans la lecture d'instructions. Retourne uniquement l'instruction textuelle, rien d'autre."
        },
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${base64Image}`
              }
            }
          ]
        }
      ],
      max_tokens: 100
    });

    return response.choices[0]?.message?.content || "";
  } catch (error) {
    console.error('Erreur lors de l\'extraction de l\'instruction avec OpenAI:', error);
    throw error;
  }
}

/**
 * Extrait le nombre cible de l'instruction (ex: "905" dans "Please select all boxes with number 905")
 * @param instruction - L'instruction extraite
 * @returns Le nombre cible
 */
function extractTargetNumber(instruction: string): string | null {
  // Recherche du pattern "number XXX" ou simplement des chiffres dans l'instruction
  const numberPattern = /number\s+(\d+)|(\d{3,})/i;
  const match = instruction.match(numberPattern);

  if (match) {
    return match[1] || match[2];
  }

  return null;
}

/**
 * Découpe l'image en grille 3x3
 * @param imageBuffer - Buffer de l'image
 * @returns Un tableau des 9 cases en buffer
 */
async function splitImageIntoGrid(imageBuffer: Buffer): Promise<Buffer[]> {
  try {
    const image = sharp(imageBuffer);
    const metadata = await image.metadata();

    if (!metadata.width || !metadata.height) {
      throw new Error("Impossible de lire les dimensions de l'image");
    }

    // Déterminer les dimensions de la grille (supposons que c'est une grille 3x3)
    const fullWidth = metadata.width;
    const fullHeight = metadata.height;

    // On estime que la grille occupe les 70% inférieurs de l'image (30% supérieur pour l'instruction)
    const gridTop = Math.floor(fullHeight * 0.3);
    const gridHeight = fullHeight - gridTop;

    const cellWidth = Math.floor(fullWidth / 3);
    const cellHeight = Math.floor(gridHeight / 3);

    const cellBuffers: Buffer[] = [];

    // Découper chaque cellule
    for (let row = 0; row < 3; row++) {
      for (let col = 0; col < 3; col++) {
        const left = col * cellWidth;
        const top = gridTop + row * cellHeight;

        const cellBuffer = await image
          .extract({
            left,
            top,
            width: cellWidth,
            height: cellHeight
          })
          .toBuffer();

        cellBuffers.push(cellBuffer);
      }
    }

    return cellBuffers;
  } catch (error) {
    console.error('Erreur lors du découpage de l\'image:', error);
    throw error;
  }
}

/**
 * Reconnaissance de texte sur une image avec Tesseract
 * @param imageBuffer - Buffer de l'image
 * @returns Le texte reconnu
 */
async function recognizeText(imageBuffer: Buffer): Promise<string> {
  try {
    // Préprocessing de l'image pour améliorer la reconnaissance
    const processedBuffer = await sharp(imageBuffer)
      .resize(200, 200, { fit: 'contain', background: { r: 255, g: 255, b: 255, alpha: 1 } })
      .threshold(128)
      .toBuffer();

    const { data } = await Tesseract.recognize(
      processedBuffer,
      'eng', // langue anglaise
      {
        logger: m => console.log(m)
      }
    );

    // Nettoyage et récupération du texte - ne garder que les chiffres
    const cleanText = data.text.trim().replace(/[^0-9]/g, '');
    console.log(`OCR raw: "${data.text}" -> cleaned: "${cleanText}"`);
    return cleanText;
  } catch (error) {
    console.error('Erreur lors de la reconnaissance de texte:', error);
    return '';
  }
}

/**
 * Analyse la grille pour trouver les cases correspondant au nombre cible
 * @param cellBuffers - Les buffers des cases de la grille
 * @param targetNumber - Le nombre cible à trouver
 * @returns Le résultat de l'analyse
 */
async function analyzeGrid(cellBuffers: Buffer[], targetNumber: string): Promise<GridAnalysisResult> {
  const cells: CellPosition[] = [];
  const matchingCells: CellPosition[] = [];
  const coordinates: Array<[number, number]> = [];

  for (let i = 0; i < cellBuffers.length; i++) {
    const row = Math.floor(i / 3) + 1; // 1-based indexing as requested
    const col = (i % 3) + 1; // 1-based indexing as requested

    const recognizedText = await recognizeText(cellBuffers[i]);
    console.log(`Case (${row},${col}): Texte reconnu = "${recognizedText}"`);

    const cell: CellPosition = {
      row: row - 1, // Keep 0-based for internal use
      col: col - 1, // Keep 0-based for internal use
      label: recognizedText
    };

    cells.push(cell);

    // Vérifier si cette cellule contient le nombre cible
    // Essayer plusieurs méthodes de correspondance
    const isMatch = recognizedText === targetNumber ||
                   recognizedText.includes(targetNumber) ||
                   targetNumber.includes(recognizedText) ||
                   (recognizedText.length >= 3 && targetNumber.length >= 3 &&
                    recognizedText.substring(0, 3) === targetNumber.substring(0, 3));

    if (isMatch) {
      matchingCells.push(cell);
      coordinates.push([row, col]); // 1-based coordinates for output
      console.log(`✅ Correspondance trouvée en (${row},${col}): "${recognizedText}" correspond à "${targetNumber}"`);
    }
  }

  return {
    targetNumber,
    cells,
    matchingCells,
    coordinates
  };
}

/**
 * Convertit les positions des cellules en format lisible (ex: "top-left, middle-center")
 * @param cells - Les cellules correspondantes
 * @returns Description des positions en format lisible
 */
function formatPositions(cells: CellPosition[]): string {
  if (cells.length === 0) {
    return "unknown";
  }

  const positionMapping: Record<string, string> = {
    "0,0": "top-left",
    "0,1": "top-center",
    "0,2": "top-right",
    "1,0": "middle-left",
    "1,1": "middle-center",
    "1,2": "middle-right",
    "2,0": "bottom-left",
    "2,1": "bottom-center",
    "2,2": "bottom-right"
  };

  return cells.map(cell => {
    const key = `${cell.row},${cell.col}`;
    return positionMapping[key] || key;
  }).join(", ");
}

/**
 * Résout le CAPTCHA en combinant OpenAI et OCR local
 * @param input - Entrée validée
 * @returns Résultat de l'analyse
 */
export async function solve_captcha(input: CaptchaInput): Promise<{result: string, coordinates: Array<[number, number]>}> {
  try {
    const base64Image = input.image_base64;
    const imageBuffer = base64ToBuffer(base64Image);

    // Étape 1: Utiliser OpenAI pour extraire l'instruction
    console.log("Étape 1: Extraction de l'instruction avec OpenAI...");
    const instruction = await extractInstructionWithOpenAI(base64Image);
    console.log(`Instruction extraite: "${instruction}"`);

    // Étape 2: Extraire le nombre cible de l'instruction
    const targetNumber = extractTargetNumber(instruction);
    console.log(`Nombre cible extrait: "${targetNumber}"`);

    if (!targetNumber) {
      // Si on ne peut pas extraire le nombre cible, on utilise la méthode complète d'OpenAI
      console.log("Nombre cible non trouvé, utilisation de la méthode complète OpenAI...");
      const openaiResult = await solveWithOpenAI(base64Image);
      return { result: openaiResult, coordinates: [] };
    }

    // Étape 3: Découper l'image en grille 3x3
    console.log("Étape 3: Découpage de l'image en grille...");
    const cellBuffers = await splitImageIntoGrid(imageBuffer);

    // Étape 4: Analyser chaque case avec OCR et identifier les correspondances
    console.log("Étape 4: Analyse OCR de chaque case...");
    const gridAnalysis = await analyzeGrid(cellBuffers, targetNumber);

    // Étape 5: Formater les positions des cases correspondantes
    const result = formatPositions(gridAnalysis.matchingCells);

    // Si aucune correspondance n'a été trouvée avec OCR, utiliser la méthode OpenAI
    if (gridAnalysis.matchingCells.length === 0) {
      console.log("Aucune correspondance trouvée avec OCR, utilisation de la méthode complète OpenAI...");
      const openaiResult = await solveWithOpenAI(base64Image);
      return { result: openaiResult, coordinates: [] };
    }

    return { result, coordinates: gridAnalysis.coordinates };
  } catch (error) {
    console.error('Erreur lors de la résolution du CAPTCHA:', error);
    // En cas d'erreur, on utilise la méthode OpenAI comme fallback
    console.log("Erreur lors de l'analyse hybride, utilisation de la méthode complète OpenAI...");
    try {
      const openaiResult = await solveWithOpenAI(input.image_base64);
      return { result: openaiResult, coordinates: [] };
    } catch (openaiError) {
      console.error('Erreur lors du fallback OpenAI:', openaiError);
      throw error; // On renvoie l'erreur originale
    }
  }
}

/**
 * Résout le CAPTCHA en utilisant uniquement l'API OpenAI (méthode de fallback)
 * @param base64Image - Image encodée en base64
 * @returns Résultat de l'analyse
 */
async function solveWithOpenAI(base64Image: string): Promise<string> {
  const prompt = "Cette image contient un CAPTCHA du type 'Select all boxes with X'. Analyse l'image et indique quelles cases contiennent l'élément demandé. Réponds uniquement par les positions (ex: 'top-left, middle-right, bottom-center'). Si tu ne peux pas déterminer, réponds 'unknown'.";

  const response = await openai.chat.completions.create({
    model: "gpt-4o",
    messages: [
      {
        role: "system",
        content: "Tu es un agent spécialisé dans la résolution de CAPTCHA visuels. Tu dois identifier quelles cases contiennent l'élément demandé."
      },
      {
        role: "user",
        content: [
          { type: "text", text: prompt },
          {
            type: "image_url",
            image_url: {
              url: `data:image/jpeg;base64,${base64Image}`
            }
          }
        ]
      }
    ],
    max_tokens: 100
  });

  return response.choices[0]?.message?.content || "unknown";
}

/**
 * Formate la réponse pour l'API
 * @param result - Résultat de l'analyse
 * @param coordinates - Coordonnées des cellules correspondantes
 * @returns Réponse formatée
 */
export function format_response(result: string, coordinates?: Array<[number, number]>): CaptchaOutput {
  return {
    success: true,
    result: result,
    coordinates: coordinates,
    timestamp: new Date().toISOString()
  };
}

/**
 * Gère les erreurs et formate la réponse d'erreur
 * @param error - Erreur capturée
 * @returns Réponse d'erreur formatée
 */
export function handle_error(error: any): CaptchaOutput {
  return {
    success: false,
    result: "error",
    timestamp: new Date().toISOString(),
    error: error.message || "Une erreur inconnue s'est produite"
  };
}
