{"name": "tesseract.js-core", "version": "5.1.1", "description": "Tesseract C++ API in Pure Javascript", "main": "index.js", "scripts": {"test": "mocha ./tests/unit/*.test.js", "example": "http-server -p 3000"}, "files": ["index.js", "tesseract-core.js", "tesseract-core.wasm", "tesseract-core.wasm.js", "tesseract-core-lstm.js", "tesseract-core-lstm.wasm", "tesseract-core-lstm.wasm.js", "tesseract-core-simd.js", "tesseract-core-simd.wasm", "tesseract-core-simd.wasm.js", "tesseract-core-simd-lstm.js", "tesseract-core-simd-lstm.wasm", "tesseract-core-simd-lstm.wasm.js"], "repository": {"type": "git", "url": "https://github.com/naptha/tesseract.js-core.git"}, "keywords": ["ocr", "tesseract", "javascript", "emscripten", "port", "c++", "api", "recognize", "text", "computer", "vision", "vision", "language", "multilingual", "english"], "author": "antimatter15", "contributors": ["j<PERSON><PERSON><PERSON>"], "license": "Apache-2.0", "bugs": {"url": "https://github.com/naptha/tesseract.js-core/issues"}, "homepage": "https://github.com/naptha/tesseract.js-core", "devDependencies": {"eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "expect.js": "^0.3.1", "http-server": "^14.1.1", "mocha": "^10.2.0", "tesseract.js-utils": "^1.0.0-beta.8"}}