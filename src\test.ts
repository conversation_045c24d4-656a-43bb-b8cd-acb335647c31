import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import dotenv from 'dotenv';

// Charger les variables d'environnement
dotenv.config();

// URL de l'API
const API_URL = `http://localhost:${process.env.PORT || 3000}/api/solve`;

/**
 * Fonction principale de test
 */
async function runTest() {
  try {
    console.log('Démarrage du test de l\'agent CAPTCHA...');

    // Chemin vers l'image de test (à remplacer par une vraie image de CAPTCHA)
    const testImagePath = path.join(__dirname, '../test-images/captcha-test.png');

    // Vérifier si le fichier existe
    if (!fs.existsSync(testImagePath)) {
      console.error(`Erreur: L'image de test n'existe pas à l'emplacement: ${testImagePath}`);
      console.log('Veuillez créer un dossier "test-images" à la racine du projet et y ajouter une image "captcha-test.png"');
      return;
    }

    // Lire l'image et la convertir en base64
    const imageBuffer = fs.readFileSync(testImagePath);
    const base64Image = imageBuffer.toString('base64');

    console.log('Image chargée et convertie en base64');

    // Préparer la requête
    const requestData = {
      image_base64: base64Image
    };

    console.log('Envoi de la requête à l\'API...');

    // Envoyer la requête
    const response = await axios.post(API_URL, requestData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Afficher la réponse
    console.log('\nRéponse reçue:');
    console.log(JSON.stringify(response.data, null, 2));

    if (response.data.success) {
      console.log('\n✅ Test réussi! L\'agent a analysé l\'image avec succès.');
      console.log(`Résultat: ${response.data.result}`);
    } else {
      console.log('\n❌ Test échoué! L\'agent a rencontré une erreur.');
      console.log(`Erreur: ${response.data.error}`);
    }

  } catch (error) {
    console.error('\n❌ Erreur lors du test:');

    if (axios.isAxiosError(error)) {
      if (error.response) {
        console.error('Statut:', error.response.status);
        console.error('Données:', error.response.data);
      } else if (error.request) {
        console.error('Aucune réponse reçue. Vérifiez que le serveur est en cours d\'exécution.');
      } else {
        console.error('Erreur de configuration de la requête:', error.message);
      }
    } else {
      console.error('Erreur:', error);
    }
  }
}

// Exécuter le test
runTest();
