import { parse_input, solve_captcha, format_response, handle_error } from './agent';
import * as fs from 'fs';
import * as path from 'path';
import dotenv from 'dotenv';

// Charger les variables d'environnement
dotenv.config();

/**
 * Test complet de l'agent CAPTCHA
 */
async function runComprehensiveTest() {
  console.log('🚀 Démarrage du test complet de l\'agent CAPTCHA...\n');

  try {
    // Chemin vers l'image de test
    const testImagePath = path.join(__dirname, '../test-images/captcha-test.png');

    // Vérifier si le fichier existe
    if (!fs.existsSync(testImagePath)) {
      console.error(`❌ Erreur: L'image de test n'existe pas à l'emplacement: ${testImagePath}`);
      console.log('Veuillez créer un dossier "test-images" à la racine du projet et y ajouter une image "captcha-test.png"');
      return;
    }

    console.log('📁 Image de test trouvée:', testImagePath);

    // Lire l'image et la convertir en base64
    const imageBuffer = fs.readFileSync(testImagePath);
    const base64Image = imageBuffer.toString('base64');

    console.log('🔄 Image chargée et convertie en base64');
    console.log(`📊 Taille de l'image: ${imageBuffer.length} bytes`);

    // Étape 1: Test de validation d'entrée
    console.log('\n📝 Étape 1: Test de validation d\'entrée...');
    
    try {
      const parsedInput = parse_input({ image_base64: base64Image });
      console.log('✅ Validation d\'entrée réussie');
    } catch (error) {
      console.error('❌ Erreur de validation:', error);
      return;
    }

    // Étape 2: Test de résolution du CAPTCHA
    console.log('\n🔍 Étape 2: Résolution du CAPTCHA...');
    
    const startTime = Date.now();
    
    try {
      const parsedInput = parse_input({ image_base64: base64Image });
      const { result, coordinates } = await solve_captcha(parsedInput);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log('\n🎉 Résolution réussie!');
      console.log(`⏱️  Temps d'exécution: ${duration}ms`);
      console.log(`📍 Résultat: ${result}`);
      console.log(`📐 Coordonnées: ${JSON.stringify(coordinates)}`);

      // Étape 3: Test de formatage de réponse
      console.log('\n📋 Étape 3: Formatage de la réponse...');
      
      const formattedResponse = format_response(result, coordinates);
      console.log('✅ Réponse formatée:');
      console.log(JSON.stringify(formattedResponse, null, 2));

      // Analyse des résultats
      console.log('\n📊 Analyse des résultats:');
      
      if (coordinates && coordinates.length > 0) {
        console.log(`✅ ${coordinates.length} cellule(s) correspondante(s) trouvée(s)`);
        coordinates.forEach((coord, index) => {
          console.log(`   ${index + 1}. Position (${coord[0]}, ${coord[1]})`);
        });
      } else {
        console.log('⚠️  Aucune coordonnée spécifique trouvée (utilisation du fallback OpenAI)');
      }

      // Test de différents formats d'entrée
      console.log('\n🧪 Étape 4: Tests de robustesse...');
      
      // Test avec entrée invalide
      try {
        parse_input({ invalid: 'data' });
        console.log('❌ Le test d\'entrée invalide a échoué');
      } catch (error) {
        console.log('✅ Gestion d\'erreur pour entrée invalide: OK');
      }

      // Test avec base64 invalide
      try {
        parse_input({ image_base64: 'invalid_base64!' });
        console.log('❌ Le test de base64 invalide a échoué');
      } catch (error) {
        console.log('✅ Gestion d\'erreur pour base64 invalide: OK');
      }

      console.log('\n🎯 Test complet terminé avec succès!');
      
      // Résumé final
      console.log('\n📈 Résumé:');
      console.log(`   • Temps total: ${duration}ms`);
      console.log(`   • Résultat: ${result}`);
      console.log(`   • Coordonnées: ${coordinates?.length || 0} position(s)`);
      console.log(`   • Statut: Succès ✅`);

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.error('\n❌ Erreur lors de la résolution du CAPTCHA:');
      console.error(error);
      
      // Test de gestion d'erreur
      console.log('\n🔧 Test de gestion d\'erreur...');
      const errorResponse = handle_error(error);
      console.log('Réponse d\'erreur formatée:');
      console.log(JSON.stringify(errorResponse, null, 2));
      
      console.log(`\n⏱️  Temps avant erreur: ${duration}ms`);
    }

  } catch (error) {
    console.error('\n💥 Erreur critique lors du test:');
    console.error(error);
  }
}

// Fonction pour afficher les informations système
function displaySystemInfo() {
  console.log('🖥️  Informations système:');
  console.log(`   • Node.js: ${process.version}`);
  console.log(`   • Plateforme: ${process.platform}`);
  console.log(`   • Architecture: ${process.arch}`);
  console.log(`   • Mémoire: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB utilisés`);
  console.log('');
}

// Exécuter le test
console.log('🔬 CAPTCHA Agent - Test Complet\n');
displaySystemInfo();
runComprehensiveTest();
