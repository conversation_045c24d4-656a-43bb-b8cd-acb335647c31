import express from 'express';
import dotenv from 'dotenv';
import path from 'path';
import { parse_input, solve_captcha, format_response, handle_error, CaptchaOutput } from './agent';

// Charger les variables d'environnement
dotenv.config();

// Initialiser le serveur Express
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware pour parser le JSON
app.use(express.json({ limit: '10mb' }));

// Servir les fichiers statiques depuis le dossier public
app.use(express.static(path.join(__dirname, '../public')));

// Route principale pour résoudre les CAPTCHA
app.post('/api/solve', async (req, res) => {
  try {
    // Étape 1: Parser et valider l'entrée
    const parsedInput = parse_input(req.body);

    // Étape 2: Résoudre le CAPTCHA
    const { result, coordinates } = await solve_captcha(parsedInput);

    // Étape 3: Formater la réponse
    const formattedResponse = format_response(result, coordinates);

    // Renvoyer la réponse
    res.json(formattedResponse);
  } catch (error) {
    // Gérer les erreurs
    console.error('Erreur lors du traitement de la requête:', error);
    const errorResponse = handle_error(error);
    res.status(400).json(errorResponse);
  }
});

// Route de test pour vérifier que le serveur fonctionne
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Route racine pour servir le frontend
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`Serveur démarré sur http://localhost:${PORT}`);
  console.log(`Endpoint CAPTCHA: http://localhost:${PORT}/api/solve`);
  console.log(`Frontend disponible sur http://localhost:${PORT}`);
});
