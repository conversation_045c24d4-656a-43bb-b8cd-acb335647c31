<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CAPTCHA Solver Agent</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .dropzone {
            border: 2px dashed #ccc;
            border-radius: 5px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            margin: 20px 0;
            transition: background-color 0.3s;
        }
        .dropzone:hover {
            background-color: #f0f0f0;
        }
        .preview-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        #preview {
            max-width: 100%;
            max-height: 300px;
            display: none;
        }
        .result-container {
            margin-top: 20px;
            display: none;
        }
        .result-box {
            background-color: #e9f7ef;
            border-left: 4px solid #2ecc71;
            padding: 15px;
            border-radius: 4px;
        }
        .error-box {
            background-color: #fdedec;
            border-left: 4px solid #e74c3c;
            padding: 15px;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 0;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CAPTCHA Solver Agent</h1>
        <p>Cet outil analyse une image de CAPTCHA et tente d'identifier les éléments qu'elle contient.</p>

        <div class="dropzone" id="dropzone">
            <p>Glissez une image de CAPTCHA ici ou cliquez pour sélectionner un fichier</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>

        <div class="preview-container">
            <img id="preview" alt="Aperçu de l'image">
        </div>

        <div style="text-align: center;">
            <button id="analyzeBtn" disabled>Analyser l'image</button>
        </div>

        <div class="loader" id="loader"></div>

        <div class="result-container" id="resultContainer">
            <h3>Résultat de l'analyse</h3>
            <div id="resultBox" class="result-box"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const dropzone = document.getElementById('dropzone');
            const fileInput = document.getElementById('fileInput');
            const preview = document.getElementById('preview');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const loader = document.getElementById('loader');
            const resultContainer = document.getElementById('resultContainer');
            const resultBox = document.getElementById('resultBox');

            let imageBase64 = null;

            // Événements de glisser-déposer
            dropzone.addEventListener('click', () => fileInput.click());

            dropzone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropzone.style.borderColor = '#4CAF50';
            });

            dropzone.addEventListener('dragleave', () => {
                dropzone.style.borderColor = '#ccc';
            });

            dropzone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropzone.style.borderColor = '#ccc';

                if (e.dataTransfer.files.length) {
                    handleFile(e.dataTransfer.files[0]);
                }
            });

            fileInput.addEventListener('change', () => {
                if (fileInput.files.length) {
                    handleFile(fileInput.files[0]);
                }
            });

            analyzeBtn.addEventListener('click', analyzeCaptcha);

            function handleFile(file) {
                if (!file.type.match('image.*')) {
                    alert('Veuillez sélectionner une image.');
                    return;
                }

                const reader = new FileReader();

                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';

                    // Extraire la partie base64 de l'URL de données
                    imageBase64 = e.target.result.split(',')[1];
                    analyzeBtn.disabled = false;

                    // Masquer les résultats précédents
                    resultContainer.style.display = 'none';
                };

                reader.readAsDataURL(file);
            }

            async function analyzeCaptcha() {
                if (!imageBase64) return;

                // Afficher le loader
                analyzeBtn.disabled = true;
                loader.style.display = 'block';
                resultContainer.style.display = 'none';

                try {
                    const response = await fetch('/api/solve', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            image_base64: imageBase64
                        })
                    });

                    const data = await response.json();

                    // Afficher les résultats
                    if (data.success) {
                        resultBox.className = 'result-box';
                        resultBox.innerHTML = `
                            <p><strong>Résultat :</strong> ${data.result}</p>
                            <p><strong>Horodatage :</strong> ${new Date(data.timestamp).toLocaleString()}</p>
                        `;
                    } else {
                        resultBox.className = 'error-box';
                        resultBox.innerHTML = `
                            <p><strong>Erreur :</strong> ${data.error || 'Une erreur inconnue s\'est produite'}</p>
                            <p><strong>Horodatage :</strong> ${new Date(data.timestamp).toLocaleString()}</p>
                        `;
                    }

                } catch (error) {
                    resultBox.className = 'error-box';
                    resultBox.innerHTML = `<p><strong>Erreur :</strong> ${error.message || 'Une erreur inconnue s\'est produite'}</p>`;
                } finally {
                    // Masquer le loader et afficher les résultats
                    loader.style.display = 'none';
                    resultContainer.style.display = 'block';
                    analyzeBtn.disabled = false;
                }
            }
        });
    </script>
</body>
</html>
