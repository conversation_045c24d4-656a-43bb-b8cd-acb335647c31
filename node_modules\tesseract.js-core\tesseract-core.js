
var TesseractCore = (() => {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(TesseractCore = {})  {

var b;b||(b=typeof TesseractCore !== 'undefined' ? TesseractCore : {});var aa,ba;b.ready=new Promise((a,c)=>{aa=a;ba=c});var ca=Object.assign({},b),da="./this.program",ea=(a,c)=>{throw c;},fa="object"==typeof window,ha="function"==typeof importScripts,ia="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,f="",ja,ka,la;
if(ia){var fs=require("fs"),ma=require("path");f=ha?ma.dirname(f)+"/":__dirname+"/";ja=(a,c)=>{a=a.startsWith("file://")?new URL(a):ma.normalize(a);return fs.readFileSync(a,c?void 0:"utf8")};la=a=>{a=ja(a,!0);a.buffer||(a=new Uint8Array(a));return a};ka=(a,c,d,e=!0)=>{a=a.startsWith("file://")?new URL(a):ma.normalize(a);fs.readFile(a,e?void 0:"utf8",(g,h)=>{g?d(g):c(e?h.buffer:h)})};!b.thisProgram&&1<process.argv.length&&(da=process.argv[1].replace(/\\/g,"/"));process.argv.slice(2);ea=(a,c)=>{process.exitCode=
a;throw c;};b.inspect=()=>"[Emscripten Module object]"}else if(fa||ha)ha?f=self.location.href:"undefined"!=typeof document&&document.currentScript&&(f=document.currentScript.src),_scriptDir&&(f=_scriptDir),0!==f.indexOf("blob:")?f=f.substr(0,f.replace(/[?#].*/,"").lastIndexOf("/")+1):f="",ja=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.send(null);return c.responseText},ha&&(la=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}),
ka=(a,c,d)=>{var e=new XMLHttpRequest;e.open("GET",a,!0);e.responseType="arraybuffer";e.onload=()=>{200==e.status||0==e.status&&e.response?c(e.response):d()};e.onerror=d;e.send(null)};var na=b.print||console.log.bind(console),oa=b.printErr||console.warn.bind(console);Object.assign(b,ca);ca=null;b.thisProgram&&(da=b.thisProgram);b.quit&&(ea=b.quit);var pa;b.wasmBinary&&(pa=b.wasmBinary);var noExitRuntime=b.noExitRuntime||!0;"object"!=typeof WebAssembly&&n("no native wasm support detected");
var ra,sa=!1,p,ta,ua,r,u,va,wa;function xa(){var a=ra.buffer;b.HEAP8=p=new Int8Array(a);b.HEAP16=ua=new Int16Array(a);b.HEAP32=r=new Int32Array(a);b.HEAPU8=ta=new Uint8Array(a);b.HEAPU16=new Uint16Array(a);b.HEAPU32=u=new Uint32Array(a);b.HEAPF32=va=new Float32Array(a);b.HEAPF64=wa=new Float64Array(a)}var ya,za=[],Aa=[],Ba=[],Ca=!1;function Da(){var a=b.preRun.shift();za.unshift(a)}var Ea=0,Fa=null,Ga=null;function Ha(){Ea++;b.monitorRunDependencies&&b.monitorRunDependencies(Ea)}
function Ia(){Ea--;b.monitorRunDependencies&&b.monitorRunDependencies(Ea);if(0==Ea&&(null!==Fa&&(clearInterval(Fa),Fa=null),Ga)){var a=Ga;Ga=null;a()}}function n(a){if(b.onAbort)b.onAbort(a);a="Aborted("+a+")";oa(a);sa=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");ba(a);throw a;}function Ja(a){return a.startsWith("data:application/octet-stream;base64,")}var Ka;Ka="tesseract-core.wasm";if(!Ja(Ka)){var La=Ka;Ka=b.locateFile?b.locateFile(La,f):f+La}
function Ma(a){try{if(a==Ka&&pa)return new Uint8Array(pa);if(la)return la(a);throw"both async and sync fetching of the wasm failed";}catch(c){n(c)}}function Na(a){if(!pa&&(fa||ha)){if("function"==typeof fetch&&!a.startsWith("file://"))return fetch(a,{credentials:"same-origin"}).then(c=>{if(!c.ok)throw"failed to load wasm binary file at '"+a+"'";return c.arrayBuffer()}).catch(()=>Ma(a));if(ka)return new Promise((c,d)=>{ka(a,e=>c(new Uint8Array(e)),d)})}return Promise.resolve().then(()=>Ma(a))}
function Oa(a,c,d){return Na(a).then(e=>WebAssembly.instantiate(e,c)).then(e=>e).then(d,e=>{oa("failed to asynchronously prepare wasm: "+e);n(e)})}
function Pa(a,c){var d=Ka;return pa||"function"!=typeof WebAssembly.instantiateStreaming||Ja(d)||d.startsWith("file://")||ia||"function"!=typeof fetch?Oa(d,a,c):fetch(d,{credentials:"same-origin"}).then(e=>WebAssembly.instantiateStreaming(e,a).then(c,function(g){oa("wasm streaming compile failed: "+g);oa("falling back to ArrayBuffer instantiation");return Oa(d,a,c)}))}
var x,y,Qa={629308:a=>{b.TesseractProgress&&b.TesseractProgress(a)},629377:a=>{b.TesseractProgress&&b.TesseractProgress(a)},629446:a=>{b.TesseractProgress&&b.TesseractProgress(a)}};function Ra(a){this.name="ExitStatus";this.message="Program terminated with exit("+a+")";this.status=a}function Sa(a){for(;0<a.length;)a.shift()(b)}function Ta(a){for(var c=0,d=0;d<a.length;++d){var e=a.charCodeAt(d);127>=e?c++:2047>=e?c+=2:55296<=e&&57343>=e?(c+=4,++d):c+=3}return c}
function Ua(a,c,d,e){if(!(0<e))return 0;var g=d;e=d+e-1;for(var h=0;h<a.length;++h){var k=a.charCodeAt(h);if(55296<=k&&57343>=k){var m=a.charCodeAt(++h);k=65536+((k&1023)<<10)|m&1023}if(127>=k){if(d>=e)break;c[d++]=k}else{if(2047>=k){if(d+1>=e)break;c[d++]=192|k>>6}else{if(65535>=k){if(d+2>=e)break;c[d++]=224|k>>12}else{if(d+3>=e)break;c[d++]=240|k>>18;c[d++]=128|k>>12&63}c[d++]=128|k>>6&63}c[d++]=128|k&63}}c[d]=0;return d-g}var Va="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;
function Wa(a,c){for(var d=c+NaN,e=c;a[e]&&!(e>=d);)++e;if(16<e-c&&a.buffer&&Va)return Va.decode(a.subarray(c,e));for(d="";c<e;){var g=a[c++];if(g&128){var h=a[c++]&63;if(192==(g&224))d+=String.fromCharCode((g&31)<<6|h);else{var k=a[c++]&63;g=224==(g&240)?(g&15)<<12|h<<6|k:(g&7)<<18|h<<12|k<<6|a[c++]&63;65536>g?d+=String.fromCharCode(g):(g-=65536,d+=String.fromCharCode(55296|g>>10,56320|g&1023))}}else d+=String.fromCharCode(g)}return d}function z(a){return a?Wa(ta,a):""}
function Xa(a,c="i8"){c.endsWith("*")&&(c="*");switch(c){case "i1":return p[a>>0];case "i8":return p[a>>0];case "i16":return ua[a>>1];case "i32":return r[a>>2];case "i64":return r[a>>2];case "float":return va[a>>2];case "double":return wa[a>>3];case "*":return u[a>>2];default:n("invalid type for getValue: "+c)}}
function Ya(a,c,d="i8"){d.endsWith("*")&&(d="*");switch(d){case "i1":p[a>>0]=c;break;case "i8":p[a>>0]=c;break;case "i16":ua[a>>1]=c;break;case "i32":r[a>>2]=c;break;case "i64":y=[c>>>0,(x=c,1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[a>>2]=y[0];r[a+4>>2]=y[1];break;case "float":va[a>>2]=c;break;case "double":wa[a>>3]=c;break;case "*":u[a>>2]=c;break;default:n("invalid type for setValue: "+d)}}
function Za(a){this.Cf=a-24;this.rh=function(c){u[this.Cf+4>>2]=c};this.Cg=function(c){u[this.Cf+8>>2]=c};this.cg=function(c,d){this.Pf();this.rh(c);this.Cg(d)};this.Pf=function(){u[this.Cf+16>>2]=0}}
var $a=0,ab=0,bb=(a,c)=>{for(var d=0,e=a.length-1;0<=e;e--){var g=a[e];"."===g?a.splice(e,1):".."===g?(a.splice(e,1),d++):d&&(a.splice(e,1),d--)}if(c)for(;d;d--)a.unshift("..");return a},cb=a=>{var c="/"===a.charAt(0),d="/"===a.substr(-1);(a=bb(a.split("/").filter(e=>!!e),!c).join("/"))||c||(a=".");a&&d&&(a+="/");return(c?"/":"")+a},db=a=>{var c=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(a).slice(1);a=c[0];c=c[1];if(!a&&!c)return".";c&&(c=c.substr(0,c.length-1));return a+
c},eb=a=>{if("/"===a)return"/";a=cb(a);a=a.replace(/\/$/,"");var c=a.lastIndexOf("/");return-1===c?a:a.substr(c+1)},fb=(a,c)=>cb(a+"/"+c);function gb(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return d=>crypto.getRandomValues(d);if(ia)try{var a=require("crypto");if(a.randomFillSync)return d=>a.randomFillSync(d);var c=a.randomBytes;return d=>(d.set(c(d.byteLength)),d)}catch(d){}n("initRandomDevice")}function hb(a){return(hb=gb())(a)}
function ib(){for(var a="",c=!1,d=arguments.length-1;-1<=d&&!c;d--){c=0<=d?arguments[d]:A.cwd();if("string"!=typeof c)throw new TypeError("Arguments to path.resolve must be strings");if(!c)return"";a=c+"/"+a;c="/"===c.charAt(0)}a=bb(a.split("/").filter(e=>!!e),!c).join("/");return(c?"/":"")+a||"."}
var jb=(a,c)=>{function d(k){for(var m=0;m<k.length&&""===k[m];m++);for(var v=k.length-1;0<=v&&""===k[v];v--);return m>v?[]:k.slice(m,v-m+1)}a=ib(a).substr(1);c=ib(c).substr(1);a=d(a.split("/"));c=d(c.split("/"));for(var e=Math.min(a.length,c.length),g=e,h=0;h<e;h++)if(a[h]!==c[h]){g=h;break}e=[];for(h=g;h<a.length;h++)e.push("..");e=e.concat(c.slice(g));return e.join("/")};function kb(a,c){var d=Array(Ta(a)+1);a=Ua(a,d,0,d.length);c&&(d.length=a);return d}var lb=[];
function mb(a,c){lb[a]={input:[],output:[],mg:c};A.Xg(a,nb)}
var nb={open:function(a){var c=lb[a.node.rdev];if(!c)throw new A.Df(43);a.tty=c;a.seekable=!1},close:function(a){a.tty.mg.fsync(a.tty)},fsync:function(a){a.tty.mg.fsync(a.tty)},read:function(a,c,d,e){if(!a.tty||!a.tty.mg.kh)throw new A.Df(60);for(var g=0,h=0;h<e;h++){try{var k=a.tty.mg.kh(a.tty)}catch(m){throw new A.Df(29);}if(void 0===k&&0===g)throw new A.Df(6);if(null===k||void 0===k)break;g++;c[d+h]=k}g&&(a.node.timestamp=Date.now());return g},write:function(a,c,d,e){if(!a.tty||!a.tty.mg.Ug)throw new A.Df(60);
try{for(var g=0;g<e;g++)a.tty.mg.Ug(a.tty,c[d+g])}catch(h){throw new A.Df(29);}e&&(a.node.timestamp=Date.now());return g}},ob={kh:function(a){if(!a.input.length){var c=null;if(ia){var d=Buffer.alloc(256),e=0;try{e=fs.readSync(process.stdin.fd,d,0,256,-1)}catch(g){if(g.toString().includes("EOF"))e=0;else throw g;}0<e?c=d.slice(0,e).toString("utf-8"):c=null}else"undefined"!=typeof window&&"function"==typeof window.prompt?(c=window.prompt("Input: "),null!==c&&(c+="\n")):"function"==typeof readline&&
(c=readline(),null!==c&&(c+="\n"));if(!c)return null;a.input=kb(c,!0)}return a.input.shift()},Ug:function(a,c){null===c||10===c?(na(Wa(a.output,0)),a.output=[]):0!=c&&a.output.push(c)},fsync:function(a){a.output&&0<a.output.length&&(na(Wa(a.output,0)),a.output=[])}},pb={Ug:function(a,c){null===c||10===c?(oa(Wa(a.output,0)),a.output=[]):0!=c&&a.output.push(c)},fsync:function(a){a.output&&0<a.output.length&&(oa(Wa(a.output,0)),a.output=[])}},B={Vf:null,Mf:function(){return B.createNode(null,"/",16895,
0)},createNode:function(a,c,d,e){if(A.ei(d)||A.isFIFO(d))throw new A.Df(63);B.Vf||(B.Vf={dir:{node:{Sf:B.Ef.Sf,Of:B.Ef.Of,lookup:B.Ef.lookup,Zf:B.Ef.Zf,rename:B.Ef.rename,unlink:B.Ef.unlink,rmdir:B.Ef.rmdir,readdir:B.Ef.readdir,symlink:B.Ef.symlink},stream:{Tf:B.Gf.Tf}},file:{node:{Sf:B.Ef.Sf,Of:B.Ef.Of},stream:{Tf:B.Gf.Tf,read:B.Gf.read,write:B.Gf.write,ng:B.Gf.ng,fg:B.Gf.fg,lg:B.Gf.lg}},link:{node:{Sf:B.Ef.Sf,Of:B.Ef.Of,readlink:B.Ef.readlink},stream:{}},$g:{node:{Sf:B.Ef.Sf,Of:B.Ef.Of},stream:A.Ah}});
d=A.createNode(a,c,d,e);A.Nf(d.mode)?(d.Ef=B.Vf.dir.node,d.Gf=B.Vf.dir.stream,d.Ff={}):A.isFile(d.mode)?(d.Ef=B.Vf.file.node,d.Gf=B.Vf.file.stream,d.Kf=0,d.Ff=null):A.qg(d.mode)?(d.Ef=B.Vf.link.node,d.Gf=B.Vf.link.stream):A.vg(d.mode)&&(d.Ef=B.Vf.$g.node,d.Gf=B.Vf.$g.stream);d.timestamp=Date.now();a&&(a.Ff[c]=d,a.timestamp=d.timestamp);return d},yi:function(a){return a.Ff?a.Ff.subarray?a.Ff.subarray(0,a.Kf):new Uint8Array(a.Ff):new Uint8Array(0)},hh:function(a,c){var d=a.Ff?a.Ff.length:0;d>=c||(c=
Math.max(c,d*(1048576>d?2:1.125)>>>0),0!=d&&(c=Math.max(c,256)),d=a.Ff,a.Ff=new Uint8Array(c),0<a.Kf&&a.Ff.set(d.subarray(0,a.Kf),0))},ni:function(a,c){if(a.Kf!=c)if(0==c)a.Ff=null,a.Kf=0;else{var d=a.Ff;a.Ff=new Uint8Array(c);d&&a.Ff.set(d.subarray(0,Math.min(c,a.Kf)));a.Kf=c}},Ef:{Sf:function(a){var c={};c.dev=A.vg(a.mode)?a.id:1;c.ino=a.id;c.mode=a.mode;c.nlink=1;c.uid=0;c.gid=0;c.rdev=a.rdev;A.Nf(a.mode)?c.size=4096:A.isFile(a.mode)?c.size=a.Kf:A.qg(a.mode)?c.size=a.link.length:c.size=0;c.atime=
new Date(a.timestamp);c.mtime=new Date(a.timestamp);c.ctime=new Date(a.timestamp);c.yh=4096;c.blocks=Math.ceil(c.size/c.yh);return c},Of:function(a,c){void 0!==c.mode&&(a.mode=c.mode);void 0!==c.timestamp&&(a.timestamp=c.timestamp);void 0!==c.size&&B.ni(a,c.size)},lookup:function(){throw A.Hg[44];},Zf:function(a,c,d,e){return B.createNode(a,c,d,e)},rename:function(a,c,d){if(A.Nf(a.mode)){try{var e=A.Yf(c,d)}catch(h){}if(e)for(var g in e.Ff)throw new A.Df(55);}delete a.parent.Ff[a.name];a.parent.timestamp=
Date.now();a.name=d;c.Ff[d]=a;c.timestamp=a.parent.timestamp;a.parent=c},unlink:function(a,c){delete a.Ff[c];a.timestamp=Date.now()},rmdir:function(a,c){var d=A.Yf(a,c),e;for(e in d.Ff)throw new A.Df(55);delete a.Ff[c];a.timestamp=Date.now()},readdir:function(a){var c=[".",".."],d;for(d in a.Ff)a.Ff.hasOwnProperty(d)&&c.push(d);return c},symlink:function(a,c,d){a=B.createNode(a,c,41471,0);a.link=d;return a},readlink:function(a){if(!A.qg(a.mode))throw new A.Df(28);return a.link}},Gf:{read:function(a,
c,d,e,g){var h=a.node.Ff;if(g>=a.node.Kf)return 0;a=Math.min(a.node.Kf-g,e);if(8<a&&h.subarray)c.set(h.subarray(g,g+a),d);else for(e=0;e<a;e++)c[d+e]=h[g+e];return a},write:function(a,c,d,e,g,h){c.buffer===p.buffer&&(h=!1);if(!e)return 0;a=a.node;a.timestamp=Date.now();if(c.subarray&&(!a.Ff||a.Ff.subarray)){if(h)return a.Ff=c.subarray(d,d+e),a.Kf=e;if(0===a.Kf&&0===g)return a.Ff=c.slice(d,d+e),a.Kf=e;if(g+e<=a.Kf)return a.Ff.set(c.subarray(d,d+e),g),e}B.hh(a,g+e);if(a.Ff.subarray&&c.subarray)a.Ff.set(c.subarray(d,
d+e),g);else for(h=0;h<e;h++)a.Ff[g+h]=c[d+h];a.Kf=Math.max(a.Kf,g+e);return e},Tf:function(a,c,d){1===d?c+=a.position:2===d&&A.isFile(a.node.mode)&&(c+=a.node.Kf);if(0>c)throw new A.Df(28);return c},ng:function(a,c,d){B.hh(a.node,c+d);a.node.Kf=Math.max(a.node.Kf,c+d)},fg:function(a,c,d,e,g){if(!A.isFile(a.node.mode))throw new A.Df(43);a=a.node.Ff;if(g&2||a.buffer!==p.buffer){if(0<d||d+c<a.length)a.subarray?a=a.subarray(d,d+c):a=Array.prototype.slice.call(a,d,d+c);d=!0;n();c=void 0;if(!c)throw new A.Df(48);
p.set(a,c)}else d=!1,c=a.byteOffset;return{Cf:c,wh:d}},lg:function(a,c,d,e){B.Gf.write(a,c,0,e,d,!1);return 0}}};function qb(a,c,d){var e="al "+a;ka(a,g=>{g||n(`Loading data file "${a}" failed (no arrayBuffer).`);c(new Uint8Array(g));e&&Ia(e)},()=>{if(d)d();else throw`Loading data file "${a}" failed.`;});e&&Ha(e)}var rb=b.preloadPlugins||[];function sb(a,c,d,e){"undefined"!=typeof Browser&&Browser.cg();var g=!1;rb.forEach(function(h){!g&&h.canHandle(c)&&(h.handle(a,c,d,e),g=!0)});return g}
function tb(a,c){var d=0;a&&(d|=365);c&&(d|=146);return d}
var A={root:null,sg:[],fh:{},streams:[],ii:1,Uf:null,eh:"/",Og:!1,oh:!0,Df:null,Hg:{},Ih:null,zg:0,Jf:(a,c={})=>{a=ib(a);if(!a)return{path:"",node:null};c=Object.assign({Fg:!0,Wg:0},c);if(8<c.Wg)throw new A.Df(32);a=a.split("/").filter(k=>!!k);for(var d=A.root,e="/",g=0;g<a.length;g++){var h=g===a.length-1;if(h&&c.parent)break;d=A.Yf(d,a[g]);e=cb(e+"/"+a[g]);A.dg(d)&&(!h||h&&c.Fg)&&(d=d.rg.root);if(!h||c.Rf)for(h=0;A.qg(d.mode);)if(d=A.readlink(e),e=ib(db(e),d),d=A.Jf(e,{Wg:c.Wg+1}).node,40<h++)throw new A.Df(32);
}return{path:e,node:d}},$f:a=>{for(var c;;){if(A.wg(a))return a=a.Mf.ph,c?"/"!==a[a.length-1]?a+"/"+c:a+c:a;c=c?a.name+"/"+c:a.name;a=a.parent}},Ng:(a,c)=>{for(var d=0,e=0;e<c.length;e++)d=(d<<5)-d+c.charCodeAt(e)|0;return(a+d>>>0)%A.Uf.length},mh:a=>{var c=A.Ng(a.parent.id,a.name);a.gg=A.Uf[c];A.Uf[c]=a},nh:a=>{var c=A.Ng(a.parent.id,a.name);if(A.Uf[c]===a)A.Uf[c]=a.gg;else for(c=A.Uf[c];c;){if(c.gg===a){c.gg=a.gg;break}c=c.gg}},Yf:(a,c)=>{var d=A.gi(a);if(d)throw new A.Df(d,a);for(d=A.Uf[A.Ng(a.id,
c)];d;d=d.gg){var e=d.name;if(d.parent.id===a.id&&e===c)return d}return A.lookup(a,c)},createNode:(a,c,d,e)=>{a=new A.sh(a,c,d,e);A.mh(a);return a},Eg:a=>{A.nh(a)},wg:a=>a===a.parent,dg:a=>!!a.rg,isFile:a=>32768===(a&61440),Nf:a=>16384===(a&61440),qg:a=>40960===(a&61440),vg:a=>8192===(a&61440),ei:a=>24576===(a&61440),isFIFO:a=>4096===(a&61440),isSocket:a=>49152===(a&49152),ih:a=>{var c=["r","w","rw"][a&3];a&512&&(c+="w");return c},hg:(a,c)=>{if(A.oh)return 0;if(!c.includes("r")||a.mode&292){if(c.includes("w")&&
!(a.mode&146)||c.includes("x")&&!(a.mode&73))return 2}else return 2;return 0},gi:a=>{var c=A.hg(a,"x");return c?c:a.Ef.lookup?0:2},Tg:(a,c)=>{try{return A.Yf(a,c),20}catch(d){}return A.hg(a,"wx")},xg:(a,c,d)=>{try{var e=A.Yf(a,c)}catch(g){return g.Lf}if(a=A.hg(a,"wx"))return a;if(d){if(!A.Nf(e.mode))return 54;if(A.wg(e)||A.$f(e)===A.cwd())return 10}else if(A.Nf(e.mode))return 31;return 0},hi:(a,c)=>a?A.qg(a.mode)?32:A.Nf(a.mode)&&("r"!==A.ih(c)||c&512)?31:A.hg(a,A.ih(c)):44,th:4096,ji:(a=0,c=A.th)=>
{for(;a<=c;a++)if(!A.streams[a])return a;throw new A.Df(33);},og:a=>A.streams[a],dh:(a,c,d)=>{A.tg||(A.tg=function(){this.Pf={}},A.tg.prototype={},Object.defineProperties(A.tg.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},flags:{get:function(){return this.Pf.flags},set:function(e){this.Pf.flags=e}},position:{get:function(){return this.Pf.position},set:function(e){this.Pf.position=e}}}));a=Object.assign(new A.tg,a);c=A.ji(c,d);a.fd=c;return A.streams[c]=a},Bh:a=>
{A.streams[a]=null},Ah:{open:a=>{a.Gf=A.Jh(a.node.rdev).Gf;a.Gf.open&&a.Gf.open(a)},Tf:()=>{throw new A.Df(70);}},Sg:a=>a>>8,zi:a=>a&255,eg:(a,c)=>a<<8|c,Xg:(a,c)=>{A.fh[a]={Gf:c}},Jh:a=>A.fh[a],jh:a=>{var c=[];for(a=[a];a.length;){var d=a.pop();c.push(d);a.push.apply(a,d.sg)}return c},qh:(a,c)=>{function d(k){A.zg--;return c(k)}function e(k){if(k){if(!e.Hh)return e.Hh=!0,d(k)}else++h>=g.length&&d(null)}"function"==typeof a&&(c=a,a=!1);A.zg++;1<A.zg&&oa("warning: "+A.zg+" FS.syncfs operations in flight at once, probably just doing extra work");
var g=A.jh(A.root.Mf),h=0;g.forEach(k=>{if(!k.type.qh)return e(null);k.type.qh(k,a,e)})},Mf:(a,c,d)=>{var e="/"===d,g=!d;if(e&&A.root)throw new A.Df(10);if(!e&&!g){var h=A.Jf(d,{Fg:!1});d=h.path;h=h.node;if(A.dg(h))throw new A.Df(10);if(!A.Nf(h.mode))throw new A.Df(54);}c={type:a,Ci:c,ph:d,sg:[]};a=a.Mf(c);a.Mf=c;c.root=a;e?A.root=a:h&&(h.rg=c,h.Mf&&h.Mf.sg.push(c));return a},Fi:a=>{a=A.Jf(a,{Fg:!1});if(!A.dg(a.node))throw new A.Df(28);a=a.node;var c=a.rg,d=A.jh(c);Object.keys(A.Uf).forEach(e=>{for(e=
A.Uf[e];e;){var g=e.gg;d.includes(e.Mf)&&A.Eg(e);e=g}});a.rg=null;a.Mf.sg.splice(a.Mf.sg.indexOf(c),1)},lookup:(a,c)=>a.Ef.lookup(a,c),Zf:(a,c,d)=>{var e=A.Jf(a,{parent:!0}).node;a=eb(a);if(!a||"."===a||".."===a)throw new A.Df(28);var g=A.Tg(e,a);if(g)throw new A.Df(g);if(!e.Ef.Zf)throw new A.Df(63);return e.Ef.Zf(e,a,c,d)},create:(a,c)=>A.Zf(a,(void 0!==c?c:438)&4095|32768,0),mkdir:(a,c)=>A.Zf(a,(void 0!==c?c:511)&1023|16384,0),Ai:(a,c)=>{a=a.split("/");for(var d="",e=0;e<a.length;++e)if(a[e]){d+=
"/"+a[e];try{A.mkdir(d,c)}catch(g){if(20!=g.Lf)throw g;}}},yg:(a,c,d)=>{"undefined"==typeof d&&(d=c,c=438);return A.Zf(a,c|8192,d)},symlink:(a,c)=>{if(!ib(a))throw new A.Df(44);var d=A.Jf(c,{parent:!0}).node;if(!d)throw new A.Df(44);c=eb(c);var e=A.Tg(d,c);if(e)throw new A.Df(e);if(!d.Ef.symlink)throw new A.Df(63);return d.Ef.symlink(d,c,a)},rename:(a,c)=>{var d=db(a),e=db(c),g=eb(a),h=eb(c);var k=A.Jf(a,{parent:!0});var m=k.node;k=A.Jf(c,{parent:!0});k=k.node;if(!m||!k)throw new A.Df(44);if(m.Mf!==
k.Mf)throw new A.Df(75);var v=A.Yf(m,g);a=jb(a,e);if("."!==a.charAt(0))throw new A.Df(28);a=jb(c,d);if("."!==a.charAt(0))throw new A.Df(55);try{var q=A.Yf(k,h)}catch(t){}if(v!==q){c=A.Nf(v.mode);if(g=A.xg(m,g,c))throw new A.Df(g);if(g=q?A.xg(k,h,c):A.Tg(k,h))throw new A.Df(g);if(!m.Ef.rename)throw new A.Df(63);if(A.dg(v)||q&&A.dg(q))throw new A.Df(10);if(k!==m&&(g=A.hg(m,"w")))throw new A.Df(g);A.nh(v);try{m.Ef.rename(v,k,h)}catch(t){throw t;}finally{A.mh(v)}}},rmdir:a=>{var c=A.Jf(a,{parent:!0}).node;
a=eb(a);var d=A.Yf(c,a),e=A.xg(c,a,!0);if(e)throw new A.Df(e);if(!c.Ef.rmdir)throw new A.Df(63);if(A.dg(d))throw new A.Df(10);c.Ef.rmdir(c,a);A.Eg(d)},readdir:a=>{a=A.Jf(a,{Rf:!0}).node;if(!a.Ef.readdir)throw new A.Df(54);return a.Ef.readdir(a)},unlink:a=>{var c=A.Jf(a,{parent:!0}).node;if(!c)throw new A.Df(44);a=eb(a);var d=A.Yf(c,a),e=A.xg(c,a,!1);if(e)throw new A.Df(e);if(!c.Ef.unlink)throw new A.Df(63);if(A.dg(d))throw new A.Df(10);c.Ef.unlink(c,a);A.Eg(d)},readlink:a=>{a=A.Jf(a).node;if(!a)throw new A.Df(44);
if(!a.Ef.readlink)throw new A.Df(28);return ib(A.$f(a.parent),a.Ef.readlink(a))},stat:(a,c)=>{a=A.Jf(a,{Rf:!c}).node;if(!a)throw new A.Df(44);if(!a.Ef.Sf)throw new A.Df(63);return a.Ef.Sf(a)},lstat:a=>A.stat(a,!0),chmod:(a,c,d)=>{a="string"==typeof a?A.Jf(a,{Rf:!d}).node:a;if(!a.Ef.Of)throw new A.Df(63);a.Ef.Of(a,{mode:c&4095|a.mode&-4096,timestamp:Date.now()})},lchmod:(a,c)=>{A.chmod(a,c,!0)},fchmod:(a,c)=>{a=A.og(a);if(!a)throw new A.Df(8);A.chmod(a.node,c)},chown:(a,c,d,e)=>{a="string"==typeof a?
A.Jf(a,{Rf:!e}).node:a;if(!a.Ef.Of)throw new A.Df(63);a.Ef.Of(a,{timestamp:Date.now()})},lchown:(a,c,d)=>{A.chown(a,c,d,!0)},fchown:(a,c,d)=>{a=A.og(a);if(!a)throw new A.Df(8);A.chown(a.node,c,d)},truncate:(a,c)=>{if(0>c)throw new A.Df(28);a="string"==typeof a?A.Jf(a,{Rf:!0}).node:a;if(!a.Ef.Of)throw new A.Df(63);if(A.Nf(a.mode))throw new A.Df(31);if(!A.isFile(a.mode))throw new A.Df(28);var d=A.hg(a,"w");if(d)throw new A.Df(d);a.Ef.Of(a,{size:c,timestamp:Date.now()})},xi:(a,c)=>{a=A.og(a);if(!a)throw new A.Df(8);
if(0===(a.flags&2097155))throw new A.Df(28);A.truncate(a.node,c)},Gi:(a,c,d)=>{a=A.Jf(a,{Rf:!0}).node;a.Ef.Of(a,{timestamp:Math.max(c,d)})},open:(a,c,d)=>{if(""===a)throw new A.Df(44);if("string"==typeof c){var e={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[c];if("undefined"==typeof e)throw Error("Unknown file open mode: "+c);c=e}d=c&64?("undefined"==typeof d?438:d)&4095|32768:0;if("object"==typeof a)var g=a;else{a=cb(a);try{g=A.Jf(a,{Rf:!(c&131072)}).node}catch(h){}}e=!1;if(c&64)if(g){if(c&128)throw new A.Df(20);
}else g=A.Zf(a,d,0),e=!0;if(!g)throw new A.Df(44);A.vg(g.mode)&&(c&=-513);if(c&65536&&!A.Nf(g.mode))throw new A.Df(54);if(!e&&(d=A.hi(g,c)))throw new A.Df(d);c&512&&!e&&A.truncate(g,0);c&=-131713;g=A.dh({node:g,path:A.$f(g),flags:c,seekable:!0,position:0,Gf:g.Gf,vi:[],error:!1});g.Gf.open&&g.Gf.open(g);!b.logReadFiles||c&1||(A.Vg||(A.Vg={}),a in A.Vg||(A.Vg[a]=1));return g},close:a=>{if(A.pg(a))throw new A.Df(8);a.Mg&&(a.Mg=null);try{a.Gf.close&&a.Gf.close(a)}catch(c){throw c;}finally{A.Bh(a.fd)}a.fd=
null},pg:a=>null===a.fd,Tf:(a,c,d)=>{if(A.pg(a))throw new A.Df(8);if(!a.seekable||!a.Gf.Tf)throw new A.Df(70);if(0!=d&&1!=d&&2!=d)throw new A.Df(28);a.position=a.Gf.Tf(a,c,d);a.vi=[];return a.position},read:(a,c,d,e,g)=>{if(0>e||0>g)throw new A.Df(28);if(A.pg(a))throw new A.Df(8);if(1===(a.flags&2097155))throw new A.Df(8);if(A.Nf(a.node.mode))throw new A.Df(31);if(!a.Gf.read)throw new A.Df(28);var h="undefined"!=typeof g;if(!h)g=a.position;else if(!a.seekable)throw new A.Df(70);c=a.Gf.read(a,c,d,
e,g);h||(a.position+=c);return c},write:(a,c,d,e,g,h)=>{if(0>e||0>g)throw new A.Df(28);if(A.pg(a))throw new A.Df(8);if(0===(a.flags&2097155))throw new A.Df(8);if(A.Nf(a.node.mode))throw new A.Df(31);if(!a.Gf.write)throw new A.Df(28);a.seekable&&a.flags&1024&&A.Tf(a,0,2);var k="undefined"!=typeof g;if(!k)g=a.position;else if(!a.seekable)throw new A.Df(70);c=a.Gf.write(a,c,d,e,g,h);k||(a.position+=c);return c},ng:(a,c,d)=>{if(A.pg(a))throw new A.Df(8);if(0>c||0>=d)throw new A.Df(28);if(0===(a.flags&
2097155))throw new A.Df(8);if(!A.isFile(a.node.mode)&&!A.Nf(a.node.mode))throw new A.Df(43);if(!a.Gf.ng)throw new A.Df(138);a.Gf.ng(a,c,d)},fg:(a,c,d,e,g)=>{if(0!==(e&2)&&0===(g&2)&&2!==(a.flags&2097155))throw new A.Df(2);if(1===(a.flags&2097155))throw new A.Df(2);if(!a.Gf.fg)throw new A.Df(43);return a.Gf.fg(a,c,d,e,g)},lg:(a,c,d,e,g)=>a.Gf.lg?a.Gf.lg(a,c,d,e,g):0,Bi:()=>0,Pg:(a,c,d)=>{if(!a.Gf.Pg)throw new A.Df(59);return a.Gf.Pg(a,c,d)},readFile:(a,c={})=>{c.flags=c.flags||0;c.encoding=c.encoding||
"binary";if("utf8"!==c.encoding&&"binary"!==c.encoding)throw Error('Invalid encoding type "'+c.encoding+'"');var d,e=A.open(a,c.flags);a=A.stat(a).size;var g=new Uint8Array(a);A.read(e,g,0,a,0);"utf8"===c.encoding?d=Wa(g,0):"binary"===c.encoding&&(d=g);A.close(e);return d},writeFile:(a,c,d={})=>{d.flags=d.flags||577;a=A.open(a,d.flags,d.mode);if("string"==typeof c){var e=new Uint8Array(Ta(c)+1);c=Ua(c,e,0,e.length);A.write(a,e,0,c,void 0,d.zh)}else if(ArrayBuffer.isView(c))A.write(a,c,0,c.byteLength,
void 0,d.zh);else throw Error("Unsupported data type");A.close(a)},cwd:()=>A.eh,chdir:a=>{a=A.Jf(a,{Rf:!0});if(null===a.node)throw new A.Df(44);if(!A.Nf(a.node.mode))throw new A.Df(54);var c=A.hg(a.node,"x");if(c)throw new A.Df(c);A.eh=a.path},Dh:()=>{A.mkdir("/tmp");A.mkdir("/home");A.mkdir("/home/<USER>")},Ch:()=>{A.mkdir("/dev");A.Xg(A.eg(1,3),{read:()=>0,write:(e,g,h,k)=>k});A.yg("/dev/null",A.eg(1,3));mb(A.eg(5,0),ob);mb(A.eg(6,0),pb);A.yg("/dev/tty",A.eg(5,0));A.yg("/dev/tty1",A.eg(6,0));
var a=new Uint8Array(1024),c=0,d=()=>{0===c&&(c=hb(a).byteLength);return a[--c]};A.Qf("/dev","random",d);A.Qf("/dev","urandom",d);A.mkdir("/dev/shm");A.mkdir("/dev/shm/tmp")},Fh:()=>{A.mkdir("/proc");var a=A.mkdir("/proc/self");A.mkdir("/proc/self/fd");A.Mf({Mf:()=>{var c=A.createNode(a,"fd",16895,73);c.Ef={lookup:(d,e)=>{var g=A.og(+e);if(!g)throw new A.Df(8);d={parent:null,Mf:{ph:"fake"},Ef:{readlink:()=>g.path}};return d.parent=d}};return c}},{},"/proc/self/fd")},Gh:()=>{b.stdin?A.Qf("/dev","stdin",
b.stdin):A.symlink("/dev/tty","/dev/stdin");b.stdout?A.Qf("/dev","stdout",null,b.stdout):A.symlink("/dev/tty","/dev/stdout");b.stderr?A.Qf("/dev","stderr",null,b.stderr):A.symlink("/dev/tty1","/dev/stderr");A.open("/dev/stdin",0);A.open("/dev/stdout",1);A.open("/dev/stderr",1)},gh:()=>{A.Df||(A.Df=function(a,c){this.name="ErrnoError";this.node=c;this.oi=function(d){this.Lf=d};this.oi(a);this.message="FS error"},A.Df.prototype=Error(),A.Df.prototype.constructor=A.Df,[44].forEach(a=>{A.Hg[a]=new A.Df(a);
A.Hg[a].stack="<generic error, no stack>"}))},pi:()=>{A.gh();A.Uf=Array(4096);A.Mf(B,{},"/");A.Dh();A.Ch();A.Fh();A.Ih={MEMFS:B}},cg:(a,c,d)=>{A.cg.Og=!0;A.gh();b.stdin=a||b.stdin;b.stdout=c||b.stdout;b.stderr=d||b.stderr;A.Gh()},Di:()=>{A.cg.Og=!1;for(var a=0;a<A.streams.length;a++){var c=A.streams[a];c&&A.close(c)}},wi:(a,c)=>{a=A.xh(a,c);return a.exists?a.object:null},xh:(a,c)=>{try{var d=A.Jf(a,{Rf:!c});a=d.path}catch(g){}var e={wg:!1,exists:!1,error:0,name:null,path:null,object:null,ki:!1,mi:null,
li:null};try{d=A.Jf(a,{parent:!0}),e.ki=!0,e.mi=d.path,e.li=d.node,e.name=eb(a),d=A.Jf(a,{Rf:!c}),e.exists=!0,e.path=d.path,e.object=d.node,e.name=d.node.name,e.wg="/"===d.path}catch(g){e.error=g.Lf}return e},Dg:(a,c)=>{a="string"==typeof a?a:A.$f(a);for(c=c.split("/").reverse();c.length;){var d=c.pop();if(d){var e=cb(a+"/"+d);try{A.mkdir(e)}catch(g){}a=e}}return e},Eh:(a,c,d,e,g)=>{a="string"==typeof a?a:A.$f(a);c=cb(a+"/"+c);return A.create(c,tb(e,g))},ug:(a,c,d,e,g,h)=>{var k=c;a&&(a="string"==
typeof a?a:A.$f(a),k=c?cb(a+"/"+c):a);a=tb(e,g);k=A.create(k,a);if(d){if("string"==typeof d){c=Array(d.length);e=0;for(g=d.length;e<g;++e)c[e]=d.charCodeAt(e);d=c}A.chmod(k,a|146);c=A.open(k,577);A.write(c,d,0,d.length,0,h);A.close(c);A.chmod(k,a)}return k},Qf:(a,c,d,e)=>{a=fb("string"==typeof a?a:A.$f(a),c);c=tb(!!d,!!e);A.Qf.Sg||(A.Qf.Sg=64);var g=A.eg(A.Qf.Sg++,0);A.Xg(g,{open:h=>{h.seekable=!1},close:()=>{e&&e.buffer&&e.buffer.length&&e(10)},read:(h,k,m,v)=>{for(var q=0,t=0;t<v;t++){try{var F=
d()}catch(U){throw new A.Df(29);}if(void 0===F&&0===q)throw new A.Df(6);if(null===F||void 0===F)break;q++;k[m+t]=F}q&&(h.node.timestamp=Date.now());return q},write:(h,k,m,v)=>{for(var q=0;q<v;q++)try{e(k[m+q])}catch(t){throw new A.Df(29);}v&&(h.node.timestamp=Date.now());return q}});return A.yg(a,c,g)},Gg:a=>{if(a.Qg||a.fi||a.link||a.Ff)return!0;if("undefined"!=typeof XMLHttpRequest)throw Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");
if(ja)try{a.Ff=kb(ja(a.url),!0),a.Kf=a.Ff.length}catch(c){throw new A.Df(29);}else throw Error("Cannot load without read() or XMLHttpRequest.");},ah:(a,c,d,e,g)=>{function h(){this.Rg=!1;this.Pf=[]}h.prototype.get=function(q){if(!(q>this.length-1||0>q)){var t=q%this.chunkSize;return this.lh(q/this.chunkSize|0)[t]}};h.prototype.Cg=function(q){this.lh=q};h.prototype.Zg=function(){var q=new XMLHttpRequest;q.open("HEAD",d,!1);q.send(null);if(!(200<=q.status&&300>q.status||304===q.status))throw Error("Couldn't load "+
d+". Status: "+q.status);var t=Number(q.getResponseHeader("Content-length")),F,U=(F=q.getResponseHeader("Accept-Ranges"))&&"bytes"===F;q=(F=q.getResponseHeader("Content-Encoding"))&&"gzip"===F;var l=1048576;U||(l=t);var w=this;w.Cg(E=>{var V=E*l,qa=(E+1)*l-1;qa=Math.min(qa,t-1);if("undefined"==typeof w.Pf[E]){var Th=w.Pf;if(V>qa)throw Error("invalid range ("+V+", "+qa+") or no bytes requested!");if(qa>t-1)throw Error("only "+t+" bytes available! programmer error!");var W=new XMLHttpRequest;W.open("GET",
d,!1);t!==l&&W.setRequestHeader("Range","bytes="+V+"-"+qa);W.responseType="arraybuffer";W.overrideMimeType&&W.overrideMimeType("text/plain; charset=x-user-defined");W.send(null);if(!(200<=W.status&&300>W.status||304===W.status))throw Error("Couldn't load "+d+". Status: "+W.status);V=void 0!==W.response?new Uint8Array(W.response||[]):kb(W.responseText||"",!0);Th[E]=V}if("undefined"==typeof w.Pf[E])throw Error("doXHR failed!");return w.Pf[E]});if(q||!t)l=t=1,l=t=this.lh(0).length,na("LazyFiles on gzip forces download of the whole file when length is accessed");
this.vh=t;this.uh=l;this.Rg=!0};if("undefined"!=typeof XMLHttpRequest){if(!ha)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var k=new h;Object.defineProperties(k,{length:{get:function(){this.Rg||this.Zg();return this.vh}},chunkSize:{get:function(){this.Rg||this.Zg();return this.uh}}});k={Qg:!1,Ff:k}}else k={Qg:!1,url:d};var m=A.Eh(a,c,k,e,g);k.Ff?m.Ff=k.Ff:k.url&&(m.Ff=null,m.url=k.url);Object.defineProperties(m,{Kf:{get:function(){return this.Ff.length}}});
var v={};Object.keys(m.Gf).forEach(q=>{var t=m.Gf[q];v[q]=function(){A.Gg(m);return t.apply(null,arguments)}});v.read=(q,t,F,U,l)=>{A.Gg(m);q=q.node.Ff;if(l>=q.length)t=0;else{U=Math.min(q.length-l,U);if(q.slice)for(var w=0;w<U;w++)t[F+w]=q[l+w];else for(w=0;w<U;w++)t[F+w]=q.get(l+w);t=U}return t};v.fg=()=>{A.Gg(m);n();throw new A.Df(48);};m.Gf=v;return m}};
function ub(a,c,d){if("/"===c.charAt(0))return c;a=-100===a?A.cwd():vb(a).path;if(0==c.length){if(!d)throw new A.Df(44);return a}return cb(a+"/"+c)}
function wb(a,c,d){try{var e=a(c)}catch(h){if(h&&h.node&&cb(c)!==cb(A.$f(h.node)))return-54;throw h;}r[d>>2]=e.dev;r[d+8>>2]=e.ino;r[d+12>>2]=e.mode;u[d+16>>2]=e.nlink;r[d+20>>2]=e.uid;r[d+24>>2]=e.gid;r[d+28>>2]=e.rdev;y=[e.size>>>0,(x=e.size,1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[d+40>>2]=y[0];r[d+44>>2]=y[1];r[d+48>>2]=4096;r[d+52>>2]=e.blocks;a=e.atime.getTime();c=e.mtime.getTime();var g=e.ctime.getTime();y=[Math.floor(a/1E3)>>>0,(x=
Math.floor(a/1E3),1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[d+56>>2]=y[0];r[d+60>>2]=y[1];u[d+64>>2]=a%1E3*1E3;y=[Math.floor(c/1E3)>>>0,(x=Math.floor(c/1E3),1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[d+72>>2]=y[0];r[d+76>>2]=y[1];u[d+80>>2]=c%1E3*1E3;y=[Math.floor(g/1E3)>>>0,(x=Math.floor(g/1E3),1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>
0:0)];r[d+88>>2]=y[0];r[d+92>>2]=y[1];u[d+96>>2]=g%1E3*1E3;y=[e.ino>>>0,(x=e.ino,1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[d+104>>2]=y[0];r[d+108>>2]=y[1];return 0}var xb=void 0;function yb(){xb+=4;return r[xb-4>>2]}function vb(a){a=A.og(a);if(!a)throw new A.Df(8);return a}function zb(){oa("missing function: setThrew");n(-1)}function Ab(a){return 0===a%4&&(0!==a%100||0===a%400)}
var Bb=[0,31,60,91,121,152,182,213,244,274,305,335],Cb=[0,31,59,90,120,151,181,212,243,273,304,334];function Db(a){return(Ab(a.getFullYear())?Bb:Cb)[a.getMonth()]+a.getDate()-1}function Eb(a){var c=Ta(a)+1,d=Fb(c);d&&Ua(a,ta,d,c);return d}var Gb=[],Hb;Hb=ia?()=>{var a=process.hrtime();return 1E3*a[0]+a[1]/1E6}:()=>performance.now();var Ib={};
function Jb(){if(!Kb){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:da||"./this.program"},c;for(c in Ib)void 0===Ib[c]?delete a[c]:a[c]=Ib[c];var d=[];for(c in a)d.push(c+"="+a[c]);Kb=d}return Kb}var Kb,Lb=[31,29,31,30,31,30,31,31,30,31,30,31],Mb=[31,28,31,30,31,30,31,31,30,31,30,31];
function Nb(a,c,d,e){function g(l,w,E){for(l="number"==typeof l?l.toString():l||"";l.length<w;)l=E[0]+l;return l}function h(l,w){return g(l,w,"0")}function k(l,w){function E(qa){return 0>qa?-1:0<qa?1:0}var V;0===(V=E(l.getFullYear()-w.getFullYear()))&&0===(V=E(l.getMonth()-w.getMonth()))&&(V=E(l.getDate()-w.getDate()));return V}function m(l){switch(l.getDay()){case 0:return new Date(l.getFullYear()-1,11,29);case 1:return l;case 2:return new Date(l.getFullYear(),0,3);case 3:return new Date(l.getFullYear(),
0,2);case 4:return new Date(l.getFullYear(),0,1);case 5:return new Date(l.getFullYear()-1,11,31);case 6:return new Date(l.getFullYear()-1,11,30)}}function v(l){var w=l.jg;for(l=new Date((new Date(l.kg+1900,0,1)).getTime());0<w;){var E=l.getMonth(),V=(Ab(l.getFullYear())?Lb:Mb)[E];if(w>V-l.getDate())w-=V-l.getDate()+1,l.setDate(1),11>E?l.setMonth(E+1):(l.setMonth(0),l.setFullYear(l.getFullYear()+1));else{l.setDate(l.getDate()+w);break}}E=new Date(l.getFullYear()+1,0,4);w=m(new Date(l.getFullYear(),
0,4));E=m(E);return 0>=k(w,l)?0>=k(E,l)?l.getFullYear()+1:l.getFullYear():l.getFullYear()-1}var q=r[e+40>>2];e={ti:r[e>>2],si:r[e+4>>2],Ag:r[e+8>>2],Yg:r[e+12>>2],Bg:r[e+16>>2],kg:r[e+20>>2],Wf:r[e+24>>2],jg:r[e+28>>2],Ei:r[e+32>>2],ri:r[e+36>>2],ui:q?z(q):""};d=z(d);q={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d",
"%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var t in q)d=d.replace(new RegExp(t,"g"),q[t]);var F="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),U="January February March April May June July August September October November December".split(" ");q={"%a":function(l){return F[l.Wf].substring(0,3)},"%A":function(l){return F[l.Wf]},"%b":function(l){return U[l.Bg].substring(0,3)},"%B":function(l){return U[l.Bg]},
"%C":function(l){return h((l.kg+1900)/100|0,2)},"%d":function(l){return h(l.Yg,2)},"%e":function(l){return g(l.Yg,2," ")},"%g":function(l){return v(l).toString().substring(2)},"%G":function(l){return v(l)},"%H":function(l){return h(l.Ag,2)},"%I":function(l){l=l.Ag;0==l?l=12:12<l&&(l-=12);return h(l,2)},"%j":function(l){for(var w=0,E=0;E<=l.Bg-1;w+=(Ab(l.kg+1900)?Lb:Mb)[E++]);return h(l.Yg+w,3)},"%m":function(l){return h(l.Bg+1,2)},"%M":function(l){return h(l.si,2)},"%n":function(){return"\n"},"%p":function(l){return 0<=
l.Ag&&12>l.Ag?"AM":"PM"},"%S":function(l){return h(l.ti,2)},"%t":function(){return"\t"},"%u":function(l){return l.Wf||7},"%U":function(l){return h(Math.floor((l.jg+7-l.Wf)/7),2)},"%V":function(l){var w=Math.floor((l.jg+7-(l.Wf+6)%7)/7);2>=(l.Wf+371-l.jg-2)%7&&w++;if(w)53==w&&(E=(l.Wf+371-l.jg)%7,4==E||3==E&&Ab(l.kg)||(w=1));else{w=52;var E=(l.Wf+7-l.jg-1)%7;(4==E||5==E&&Ab(l.kg%400-1))&&w++}return h(w,2)},"%w":function(l){return l.Wf},"%W":function(l){return h(Math.floor((l.jg+7-(l.Wf+6)%7)/7),2)},
"%y":function(l){return(l.kg+1900).toString().substring(2)},"%Y":function(l){return l.kg+1900},"%z":function(l){l=l.ri;var w=0<=l;l=Math.abs(l)/60;return(w?"+":"-")+String("0000"+(l/60*100+l%60)).slice(-4)},"%Z":function(l){return l.ui},"%%":function(){return"%"}};d=d.replace(/%%/g,"\x00\x00");for(t in q)d.includes(t)&&(d=d.replace(new RegExp(t,"g"),q[t](e)));d=d.replace(/\0\0/g,"%");t=kb(d,!1);if(t.length>c)return 0;p.set(t,a);return t.length-1}var Ob=[];
function Pb(a){var c=Ob[a];c||(a>=Ob.length&&(Ob.length=a+1),Ob[a]=c=ya.get(a));return c}function Qb(a,c,d,e){a||(a=this);this.parent=a;this.Mf=a.Mf;this.rg=null;this.id=A.ii++;this.name=c;this.mode=d;this.Ef={};this.Gf={};this.rdev=e}
Object.defineProperties(Qb.prototype,{read:{get:function(){return 365===(this.mode&365)},set:function(a){a?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146===(this.mode&146)},set:function(a){a?this.mode|=146:this.mode&=-147}},fi:{get:function(){return A.Nf(this.mode)}},Qg:{get:function(){return A.vg(this.mode)}}});A.sh=Qb;
A.bh=function(a,c,d,e,g,h,k,m,v,q){function t(l){function w(E){q&&q();m||A.ug(a,c,E,e,g,v);h&&h();Ia(U)}sb(l,F,w,()=>{k&&k();Ia(U)})||w(l)}var F=c?ib(cb(a+"/"+c)):a,U="cp "+F;Ha(U);"string"==typeof d?qb(d,l=>t(l),k):t(d)};A.pi();b.FS_createPath=A.Dg;b.FS_createDataFile=A.ug;b.FS_createPreloadedFile=A.bh;b.FS_unlink=A.unlink;b.FS_createLazyFile=A.ah;b.FS_createDevice=A.Qf;
var cc={a:function(a,c,d,e){n(`Assertion failed: ${z(a)}, at: `+[c?z(c):"unknown filename",d,e?z(e):"unknown function"])},n:function(a,c,d){(new Za(a)).cg(c,d);$a=a;ab++;throw $a;},t:function(a,c,d){xb=d;try{var e=vb(a);switch(c){case 0:var g=yb();return 0>g?-28:A.dh(e,g).fd;case 1:case 2:return 0;case 3:return e.flags;case 4:return g=yb(),e.flags|=g,0;case 5:return g=yb(),ua[g+0>>1]=2,0;case 6:case 7:return 0;case 16:case 8:return-28;case 9:return r[Rb()>>2]=28,-1;default:return-28}}catch(h){if("undefined"==
typeof A||"ErrnoError"!==h.name)throw h;return-h.Lf}},N:function(a,c){try{var d=vb(a);return wb(A.stat,d.path,c)}catch(e){if("undefined"==typeof A||"ErrnoError"!==e.name)throw e;return-e.Lf}},K:function(a,c){try{if(0===c)return-28;var d=A.cwd(),e=Ta(d)+1;if(c<e)return-68;Ua(d,ta,a,c);return e}catch(g){if("undefined"==typeof A||"ErrnoError"!==g.name)throw g;return-g.Lf}},R:function(a,c,d){xb=d;try{var e=vb(a);switch(c){case 21509:case 21505:return e.tty?0:-59;case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:return e.tty?
0:-59;case 21519:if(!e.tty)return-59;var g=yb();return r[g>>2]=0;case 21520:return e.tty?-28:-59;case 21531:return g=yb(),A.Pg(e,c,g);case 21523:return e.tty?0:-59;case 21524:return e.tty?0:-59;default:return-28}}catch(h){if("undefined"==typeof A||"ErrnoError"!==h.name)throw h;return-h.Lf}},L:function(a,c,d,e){try{c=z(c);var g=e&256;c=ub(a,c,e&4096);return wb(g?A.lstat:A.stat,c,d)}catch(h){if("undefined"==typeof A||"ErrnoError"!==h.name)throw h;return-h.Lf}},r:function(a,c,d,e){xb=e;try{c=z(c);c=
ub(a,c);var g=e?yb():0;return A.open(c,d,g).fd}catch(h){if("undefined"==typeof A||"ErrnoError"!==h.name)throw h;return-h.Lf}},B:function(a){try{return a=z(a),A.rmdir(a),0}catch(c){if("undefined"==typeof A||"ErrnoError"!==c.name)throw c;return-c.Lf}},M:function(a,c){try{return a=z(a),wb(A.stat,a,c)}catch(d){if("undefined"==typeof A||"ErrnoError"!==d.name)throw d;return-d.Lf}},C:function(a,c,d){try{return c=z(c),c=ub(a,c),0===d?A.unlink(c):512===d?A.rmdir(c):n("Invalid flags passed to unlinkat"),0}catch(e){if("undefined"==
typeof A||"ErrnoError"!==e.name)throw e;return-e.Lf}},S:function(a){do{var c=u[a>>2];a+=4;var d=u[a>>2];a+=4;var e=u[a>>2];a+=4;c=z(c);A.Dg("/",db(c),!0,!0);A.ug(c,null,p.subarray(e,e+d),!0,!0,!0)}while(u[a>>2])},P:function(){return!0},y:function(){throw Infinity;},F:function(a,c){a=new Date(1E3*(u[a>>2]+4294967296*r[a+4>>2]));r[c>>2]=a.getUTCSeconds();r[c+4>>2]=a.getUTCMinutes();r[c+8>>2]=a.getUTCHours();r[c+12>>2]=a.getUTCDate();r[c+16>>2]=a.getUTCMonth();r[c+20>>2]=a.getUTCFullYear()-1900;r[c+
24>>2]=a.getUTCDay();r[c+28>>2]=(a.getTime()-Date.UTC(a.getUTCFullYear(),0,1,0,0,0,0))/864E5|0},G:function(a,c){a=new Date(1E3*(u[a>>2]+4294967296*r[a+4>>2]));r[c>>2]=a.getSeconds();r[c+4>>2]=a.getMinutes();r[c+8>>2]=a.getHours();r[c+12>>2]=a.getDate();r[c+16>>2]=a.getMonth();r[c+20>>2]=a.getFullYear()-1900;r[c+24>>2]=a.getDay();r[c+28>>2]=Db(a)|0;r[c+36>>2]=-(60*a.getTimezoneOffset());var d=(new Date(a.getFullYear(),6,1)).getTimezoneOffset(),e=(new Date(a.getFullYear(),0,1)).getTimezoneOffset();
r[c+32>>2]=(d!=e&&a.getTimezoneOffset()==Math.min(e,d))|0},H:function(a){var c=new Date(r[a+20>>2]+1900,r[a+16>>2],r[a+12>>2],r[a+8>>2],r[a+4>>2],r[a>>2],0),d=r[a+32>>2],e=c.getTimezoneOffset(),g=(new Date(c.getFullYear(),6,1)).getTimezoneOffset(),h=(new Date(c.getFullYear(),0,1)).getTimezoneOffset(),k=Math.min(h,g);0>d?r[a+32>>2]=Number(g!=h&&k==e):0<d!=(k==e)&&(g=Math.max(h,g),c.setTime(c.getTime()+6E4*((0<d?k:g)-e)));r[a+24>>2]=c.getDay();r[a+28>>2]=Db(c)|0;r[a>>2]=c.getSeconds();r[a+4>>2]=c.getMinutes();
r[a+8>>2]=c.getHours();r[a+12>>2]=c.getDate();r[a+16>>2]=c.getMonth();r[a+20>>2]=c.getYear();return c.getTime()/1E3|0},D:function(a,c,d,e,g,h,k){try{var m=vb(e),v=A.fg(m,a,g,c,d),q=v.Cf;r[h>>2]=v.wh;u[k>>2]=q;return 0}catch(t){if("undefined"==typeof A||"ErrnoError"!==t.name)throw t;return-t.Lf}},E:function(a,c,d,e,g,h){try{var k=vb(g);if(d&2){if(!A.isFile(k.node.mode))throw new A.Df(43);if(!(e&2)){var m=ta.slice(a,a+c);A.lg(k,m,h,c,e)}}}catch(v){if("undefined"==typeof A||"ErrnoError"!==v.name)throw v;
return-v.Lf}},A:function(a,c,d){function e(v){return(v=v.toTimeString().match(/\(([A-Za-z ]+)\)$/))?v[1]:"GMT"}var g=(new Date).getFullYear(),h=new Date(g,0,1),k=new Date(g,6,1);g=h.getTimezoneOffset();var m=k.getTimezoneOffset();u[a>>2]=60*Math.max(g,m);r[c>>2]=Number(g!=m);a=e(h);c=e(k);a=Eb(a);c=Eb(c);m<g?(u[d>>2]=a,u[d+4>>2]=c):(u[d>>2]=c,u[d+4>>2]=a)},l:function(){n("")},q:function(a,c,d){Gb.length=0;var e;for(d>>=2;e=ta[c++];)d+=105!=e&d,Gb.push(105==e?r[d]:wa[d++>>1]),++d;return Qa[a].apply(null,
Gb)},m:function(){return Date.now()},O:Hb,Q:function(a,c,d){ta.copyWithin(a,c,c+d)},z:function(a){var c=ta.length;a>>>=0;if(2147483648<a)return!1;for(var d=1;4>=d;d*=2){var e=c*(1+.2/d);e=Math.min(e,a+100663296);var g=Math,h=g.min;e=Math.max(a,e);e+=(65536-e%65536)%65536;a:{var k=ra.buffer;try{ra.grow(h.call(g,2147483648,e)-k.byteLength+65535>>>16);xa();var m=1;break a}catch(v){}m=void 0}if(m)return!0}return!1},I:function(a,c){var d=0;Jb().forEach(function(e,g){var h=c+d;g=u[a+4*g>>2]=h;for(h=0;h<
e.length;++h)p[g++>>0]=e.charCodeAt(h);p[g>>0]=0;d+=e.length+1});return 0},J:function(a,c){var d=Jb();u[a>>2]=d.length;var e=0;d.forEach(function(g){e+=g.length+1});u[c>>2]=e;return 0},k:function(a){if(!noExitRuntime){if(b.onExit)b.onExit(a);sa=!0}ea(a,new Ra(a))},p:function(a){try{var c=vb(a);A.close(c);return 0}catch(d){if("undefined"==typeof A||"ErrnoError"!==d.name)throw d;return d.Lf}},s:function(a,c,d,e){try{a:{var g=vb(a);a=c;for(var h,k=c=0;k<d;k++){var m=u[a>>2],v=u[a+4>>2];a+=8;var q=A.read(g,
p,m,v,h);if(0>q){var t=-1;break a}c+=q;if(q<v)break;"undefined"!==typeof h&&(h+=q)}t=c}u[e>>2]=t;return 0}catch(F){if("undefined"==typeof A||"ErrnoError"!==F.name)throw F;return F.Lf}},w:function(a,c,d,e,g){try{c=d+2097152>>>0<4194305-!!c?(c>>>0)+4294967296*d:NaN;if(isNaN(c))return 61;var h=vb(a);A.Tf(h,c,e);y=[h.position>>>0,(x=h.position,1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[g>>2]=y[0];r[g+4>>2]=y[1];h.Mg&&0===c&&0===e&&(h.Mg=null);return 0}catch(k){if("undefined"==
typeof A||"ErrnoError"!==k.name)throw k;return k.Lf}},o:function(a,c,d,e){try{a:{var g=vb(a);a=c;for(var h,k=c=0;k<d;k++){var m=u[a>>2],v=u[a+4>>2];a+=8;var q=A.write(g,p,m,v,h);if(0>q){var t=-1;break a}c+=q;"undefined"!==typeof h&&(h+=q)}t=c}u[e>>2]=t;return 0}catch(F){if("undefined"==typeof A||"ErrnoError"!==F.name)throw F;return F.Lf}},c:Sb,f:Tb,b:Ub,h:Vb,i:Wb,e:Xb,d:Yb,g:Zb,j:$b,u:ac,v:bc,T:Nb,x:function(a,c,d,e){return Nb(a,c,d,e)}};
(function(){function a(d){d=d.exports;b.asm=d;ra=b.asm.U;xa();ya=b.asm.qf;Aa.unshift(b.asm.V);Ia("wasm-instantiate");return d}var c={a:cc};Ha("wasm-instantiate");if(b.instantiateWasm)try{return b.instantiateWasm(c,a)}catch(d){oa("Module.instantiateWasm callback failed with error: "+d),ba(d)}Pa(c,function(d){a(d.instance)}).catch(ba);return{}})();
var dc=b._emscripten_bind_ParagraphJustification___destroy___0=function(){return(dc=b._emscripten_bind_ParagraphJustification___destroy___0=b.asm.W).apply(null,arguments)},ec=b._emscripten_bind_BoolPtr___destroy___0=function(){return(ec=b._emscripten_bind_BoolPtr___destroy___0=b.asm.X).apply(null,arguments)},fc=b._emscripten_bind_TessResultRenderer_BeginDocument_1=function(){return(fc=b._emscripten_bind_TessResultRenderer_BeginDocument_1=b.asm.Y).apply(null,arguments)},gc=b._emscripten_bind_TessResultRenderer_AddImage_1=
function(){return(gc=b._emscripten_bind_TessResultRenderer_AddImage_1=b.asm.Z).apply(null,arguments)},hc=b._emscripten_bind_TessResultRenderer_EndDocument_0=function(){return(hc=b._emscripten_bind_TessResultRenderer_EndDocument_0=b.asm._).apply(null,arguments)},ic=b._emscripten_bind_TessResultRenderer_happy_0=function(){return(ic=b._emscripten_bind_TessResultRenderer_happy_0=b.asm.$).apply(null,arguments)},jc=b._emscripten_bind_TessResultRenderer_file_extension_0=function(){return(jc=b._emscripten_bind_TessResultRenderer_file_extension_0=
b.asm.aa).apply(null,arguments)},kc=b._emscripten_bind_TessResultRenderer_title_0=function(){return(kc=b._emscripten_bind_TessResultRenderer_title_0=b.asm.ba).apply(null,arguments)},lc=b._emscripten_bind_TessResultRenderer_imagenum_0=function(){return(lc=b._emscripten_bind_TessResultRenderer_imagenum_0=b.asm.ca).apply(null,arguments)},mc=b._emscripten_bind_TessResultRenderer___destroy___0=function(){return(mc=b._emscripten_bind_TessResultRenderer___destroy___0=b.asm.da).apply(null,arguments)},nc=
b._emscripten_bind_LongStarPtr___destroy___0=function(){return(nc=b._emscripten_bind_LongStarPtr___destroy___0=b.asm.ea).apply(null,arguments)},oc=b._emscripten_bind_VoidPtr___destroy___0=function(){return(oc=b._emscripten_bind_VoidPtr___destroy___0=b.asm.fa).apply(null,arguments)},pc=b._emscripten_bind_ResultIterator_ResultIterator_1=function(){return(pc=b._emscripten_bind_ResultIterator_ResultIterator_1=b.asm.ga).apply(null,arguments)},qc=b._emscripten_bind_ResultIterator_Begin_0=function(){return(qc=
b._emscripten_bind_ResultIterator_Begin_0=b.asm.ha).apply(null,arguments)},rc=b._emscripten_bind_ResultIterator_RestartParagraph_0=function(){return(rc=b._emscripten_bind_ResultIterator_RestartParagraph_0=b.asm.ia).apply(null,arguments)},sc=b._emscripten_bind_ResultIterator_IsWithinFirstTextlineOfParagraph_0=function(){return(sc=b._emscripten_bind_ResultIterator_IsWithinFirstTextlineOfParagraph_0=b.asm.ja).apply(null,arguments)},tc=b._emscripten_bind_ResultIterator_RestartRow_0=function(){return(tc=
b._emscripten_bind_ResultIterator_RestartRow_0=b.asm.ka).apply(null,arguments)},uc=b._emscripten_bind_ResultIterator_Next_1=function(){return(uc=b._emscripten_bind_ResultIterator_Next_1=b.asm.la).apply(null,arguments)},vc=b._emscripten_bind_ResultIterator_IsAtBeginningOf_1=function(){return(vc=b._emscripten_bind_ResultIterator_IsAtBeginningOf_1=b.asm.ma).apply(null,arguments)},wc=b._emscripten_bind_ResultIterator_IsAtFinalElement_2=function(){return(wc=b._emscripten_bind_ResultIterator_IsAtFinalElement_2=
b.asm.na).apply(null,arguments)},xc=b._emscripten_bind_ResultIterator_Cmp_1=function(){return(xc=b._emscripten_bind_ResultIterator_Cmp_1=b.asm.oa).apply(null,arguments)},yc=b._emscripten_bind_ResultIterator_SetBoundingBoxComponents_2=function(){return(yc=b._emscripten_bind_ResultIterator_SetBoundingBoxComponents_2=b.asm.pa).apply(null,arguments)},zc=b._emscripten_bind_ResultIterator_BoundingBox_5=function(){return(zc=b._emscripten_bind_ResultIterator_BoundingBox_5=b.asm.qa).apply(null,arguments)},
Ac=b._emscripten_bind_ResultIterator_BoundingBox_6=function(){return(Ac=b._emscripten_bind_ResultIterator_BoundingBox_6=b.asm.ra).apply(null,arguments)},Bc=b._emscripten_bind_ResultIterator_BoundingBoxInternal_5=function(){return(Bc=b._emscripten_bind_ResultIterator_BoundingBoxInternal_5=b.asm.sa).apply(null,arguments)},Cc=b._emscripten_bind_ResultIterator_Empty_1=function(){return(Cc=b._emscripten_bind_ResultIterator_Empty_1=b.asm.ta).apply(null,arguments)},Dc=b._emscripten_bind_ResultIterator_BlockType_0=
function(){return(Dc=b._emscripten_bind_ResultIterator_BlockType_0=b.asm.ua).apply(null,arguments)},Ec=b._emscripten_bind_ResultIterator_BlockPolygon_0=function(){return(Ec=b._emscripten_bind_ResultIterator_BlockPolygon_0=b.asm.va).apply(null,arguments)},Fc=b._emscripten_bind_ResultIterator_GetBinaryImage_1=function(){return(Fc=b._emscripten_bind_ResultIterator_GetBinaryImage_1=b.asm.wa).apply(null,arguments)},Gc=b._emscripten_bind_ResultIterator_GetImage_5=function(){return(Gc=b._emscripten_bind_ResultIterator_GetImage_5=
b.asm.xa).apply(null,arguments)},Hc=b._emscripten_bind_ResultIterator_Baseline_5=function(){return(Hc=b._emscripten_bind_ResultIterator_Baseline_5=b.asm.ya).apply(null,arguments)},Ic=b._emscripten_bind_ResultIterator_RowAttributes_3=function(){return(Ic=b._emscripten_bind_ResultIterator_RowAttributes_3=b.asm.za).apply(null,arguments)},Jc=b._emscripten_bind_ResultIterator_Orientation_4=function(){return(Jc=b._emscripten_bind_ResultIterator_Orientation_4=b.asm.Aa).apply(null,arguments)},Kc=b._emscripten_bind_ResultIterator_ParagraphInfo_4=
function(){return(Kc=b._emscripten_bind_ResultIterator_ParagraphInfo_4=b.asm.Ba).apply(null,arguments)},Lc=b._emscripten_bind_ResultIterator_ParagraphIsLtr_0=function(){return(Lc=b._emscripten_bind_ResultIterator_ParagraphIsLtr_0=b.asm.Ca).apply(null,arguments)},Mc=b._emscripten_bind_ResultIterator_GetUTF8Text_1=function(){return(Mc=b._emscripten_bind_ResultIterator_GetUTF8Text_1=b.asm.Da).apply(null,arguments)},Nc=b._emscripten_bind_ResultIterator_SetLineSeparator_1=function(){return(Nc=b._emscripten_bind_ResultIterator_SetLineSeparator_1=
b.asm.Ea).apply(null,arguments)},Oc=b._emscripten_bind_ResultIterator_SetParagraphSeparator_1=function(){return(Oc=b._emscripten_bind_ResultIterator_SetParagraphSeparator_1=b.asm.Fa).apply(null,arguments)},Pc=b._emscripten_bind_ResultIterator_Confidence_1=function(){return(Pc=b._emscripten_bind_ResultIterator_Confidence_1=b.asm.Ga).apply(null,arguments)},Qc=b._emscripten_bind_ResultIterator_WordFontAttributes_8=function(){return(Qc=b._emscripten_bind_ResultIterator_WordFontAttributes_8=b.asm.Ha).apply(null,
arguments)},Rc=b._emscripten_bind_ResultIterator_WordRecognitionLanguage_0=function(){return(Rc=b._emscripten_bind_ResultIterator_WordRecognitionLanguage_0=b.asm.Ia).apply(null,arguments)},Sc=b._emscripten_bind_ResultIterator_WordDirection_0=function(){return(Sc=b._emscripten_bind_ResultIterator_WordDirection_0=b.asm.Ja).apply(null,arguments)},Tc=b._emscripten_bind_ResultIterator_WordIsFromDictionary_0=function(){return(Tc=b._emscripten_bind_ResultIterator_WordIsFromDictionary_0=b.asm.Ka).apply(null,
arguments)},Uc=b._emscripten_bind_ResultIterator_WordIsNumeric_0=function(){return(Uc=b._emscripten_bind_ResultIterator_WordIsNumeric_0=b.asm.La).apply(null,arguments)},Vc=b._emscripten_bind_ResultIterator_HasBlamerInfo_0=function(){return(Vc=b._emscripten_bind_ResultIterator_HasBlamerInfo_0=b.asm.Ma).apply(null,arguments)},Wc=b._emscripten_bind_ResultIterator_HasTruthString_0=function(){return(Wc=b._emscripten_bind_ResultIterator_HasTruthString_0=b.asm.Na).apply(null,arguments)},Xc=b._emscripten_bind_ResultIterator_EquivalentToTruth_1=
function(){return(Xc=b._emscripten_bind_ResultIterator_EquivalentToTruth_1=b.asm.Oa).apply(null,arguments)},Yc=b._emscripten_bind_ResultIterator_WordTruthUTF8Text_0=function(){return(Yc=b._emscripten_bind_ResultIterator_WordTruthUTF8Text_0=b.asm.Pa).apply(null,arguments)},Zc=b._emscripten_bind_ResultIterator_WordNormedUTF8Text_0=function(){return(Zc=b._emscripten_bind_ResultIterator_WordNormedUTF8Text_0=b.asm.Qa).apply(null,arguments)},$c=b._emscripten_bind_ResultIterator_WordLattice_1=function(){return($c=
b._emscripten_bind_ResultIterator_WordLattice_1=b.asm.Ra).apply(null,arguments)},ad=b._emscripten_bind_ResultIterator_SymbolIsSuperscript_0=function(){return(ad=b._emscripten_bind_ResultIterator_SymbolIsSuperscript_0=b.asm.Sa).apply(null,arguments)},bd=b._emscripten_bind_ResultIterator_SymbolIsSubscript_0=function(){return(bd=b._emscripten_bind_ResultIterator_SymbolIsSubscript_0=b.asm.Ta).apply(null,arguments)},cd=b._emscripten_bind_ResultIterator_SymbolIsDropcap_0=function(){return(cd=b._emscripten_bind_ResultIterator_SymbolIsDropcap_0=
b.asm.Ua).apply(null,arguments)},dd=b._emscripten_bind_ResultIterator___destroy___0=function(){return(dd=b._emscripten_bind_ResultIterator___destroy___0=b.asm.Va).apply(null,arguments)},ed=b._emscripten_bind_TextlineOrder___destroy___0=function(){return(ed=b._emscripten_bind_TextlineOrder___destroy___0=b.asm.Wa).apply(null,arguments)},fd=b._emscripten_bind_ETEXT_DESC___destroy___0=function(){return(fd=b._emscripten_bind_ETEXT_DESC___destroy___0=b.asm.Xa).apply(null,arguments)},gd=b._emscripten_bind_PageIterator_Begin_0=
function(){return(gd=b._emscripten_bind_PageIterator_Begin_0=b.asm.Ya).apply(null,arguments)},hd=b._emscripten_bind_PageIterator_RestartParagraph_0=function(){return(hd=b._emscripten_bind_PageIterator_RestartParagraph_0=b.asm.Za).apply(null,arguments)},jd=b._emscripten_bind_PageIterator_IsWithinFirstTextlineOfParagraph_0=function(){return(jd=b._emscripten_bind_PageIterator_IsWithinFirstTextlineOfParagraph_0=b.asm._a).apply(null,arguments)},kd=b._emscripten_bind_PageIterator_RestartRow_0=function(){return(kd=
b._emscripten_bind_PageIterator_RestartRow_0=b.asm.$a).apply(null,arguments)},ld=b._emscripten_bind_PageIterator_Next_1=function(){return(ld=b._emscripten_bind_PageIterator_Next_1=b.asm.ab).apply(null,arguments)},md=b._emscripten_bind_PageIterator_IsAtBeginningOf_1=function(){return(md=b._emscripten_bind_PageIterator_IsAtBeginningOf_1=b.asm.bb).apply(null,arguments)},nd=b._emscripten_bind_PageIterator_IsAtFinalElement_2=function(){return(nd=b._emscripten_bind_PageIterator_IsAtFinalElement_2=b.asm.cb).apply(null,
arguments)},od=b._emscripten_bind_PageIterator_Cmp_1=function(){return(od=b._emscripten_bind_PageIterator_Cmp_1=b.asm.db).apply(null,arguments)},pd=b._emscripten_bind_PageIterator_SetBoundingBoxComponents_2=function(){return(pd=b._emscripten_bind_PageIterator_SetBoundingBoxComponents_2=b.asm.eb).apply(null,arguments)},qd=b._emscripten_bind_PageIterator_BoundingBox_5=function(){return(qd=b._emscripten_bind_PageIterator_BoundingBox_5=b.asm.fb).apply(null,arguments)},rd=b._emscripten_bind_PageIterator_BoundingBox_6=
function(){return(rd=b._emscripten_bind_PageIterator_BoundingBox_6=b.asm.gb).apply(null,arguments)},sd=b._emscripten_bind_PageIterator_BoundingBoxInternal_5=function(){return(sd=b._emscripten_bind_PageIterator_BoundingBoxInternal_5=b.asm.hb).apply(null,arguments)},td=b._emscripten_bind_PageIterator_Empty_1=function(){return(td=b._emscripten_bind_PageIterator_Empty_1=b.asm.ib).apply(null,arguments)},ud=b._emscripten_bind_PageIterator_BlockType_0=function(){return(ud=b._emscripten_bind_PageIterator_BlockType_0=
b.asm.jb).apply(null,arguments)},vd=b._emscripten_bind_PageIterator_BlockPolygon_0=function(){return(vd=b._emscripten_bind_PageIterator_BlockPolygon_0=b.asm.kb).apply(null,arguments)},wd=b._emscripten_bind_PageIterator_GetBinaryImage_1=function(){return(wd=b._emscripten_bind_PageIterator_GetBinaryImage_1=b.asm.lb).apply(null,arguments)},xd=b._emscripten_bind_PageIterator_GetImage_5=function(){return(xd=b._emscripten_bind_PageIterator_GetImage_5=b.asm.mb).apply(null,arguments)},yd=b._emscripten_bind_PageIterator_Baseline_5=
function(){return(yd=b._emscripten_bind_PageIterator_Baseline_5=b.asm.nb).apply(null,arguments)},zd=b._emscripten_bind_PageIterator_Orientation_4=function(){return(zd=b._emscripten_bind_PageIterator_Orientation_4=b.asm.ob).apply(null,arguments)},Ad=b._emscripten_bind_PageIterator_ParagraphInfo_4=function(){return(Ad=b._emscripten_bind_PageIterator_ParagraphInfo_4=b.asm.pb).apply(null,arguments)},Bd=b._emscripten_bind_PageIterator___destroy___0=function(){return(Bd=b._emscripten_bind_PageIterator___destroy___0=
b.asm.qb).apply(null,arguments)},Cd=b._emscripten_bind_WritingDirection___destroy___0=function(){return(Cd=b._emscripten_bind_WritingDirection___destroy___0=b.asm.rb).apply(null,arguments)},Dd=b._emscripten_bind_WordChoiceIterator_WordChoiceIterator_1=function(){return(Dd=b._emscripten_bind_WordChoiceIterator_WordChoiceIterator_1=b.asm.sb).apply(null,arguments)},Ed=b._emscripten_bind_WordChoiceIterator_Next_0=function(){return(Ed=b._emscripten_bind_WordChoiceIterator_Next_0=b.asm.tb).apply(null,arguments)},
Fd=b._emscripten_bind_WordChoiceIterator_GetUTF8Text_0=function(){return(Fd=b._emscripten_bind_WordChoiceIterator_GetUTF8Text_0=b.asm.ub).apply(null,arguments)},Gd=b._emscripten_bind_WordChoiceIterator_Confidence_0=function(){return(Gd=b._emscripten_bind_WordChoiceIterator_Confidence_0=b.asm.vb).apply(null,arguments)},Hd=b._emscripten_bind_WordChoiceIterator___destroy___0=function(){return(Hd=b._emscripten_bind_WordChoiceIterator___destroy___0=b.asm.wb).apply(null,arguments)},Id=b._emscripten_bind_Box_get_x_0=
function(){return(Id=b._emscripten_bind_Box_get_x_0=b.asm.xb).apply(null,arguments)},Jd=b._emscripten_bind_Box_get_y_0=function(){return(Jd=b._emscripten_bind_Box_get_y_0=b.asm.yb).apply(null,arguments)},Kd=b._emscripten_bind_Box_get_w_0=function(){return(Kd=b._emscripten_bind_Box_get_w_0=b.asm.zb).apply(null,arguments)},Ld=b._emscripten_bind_Box_get_h_0=function(){return(Ld=b._emscripten_bind_Box_get_h_0=b.asm.Ab).apply(null,arguments)},Md=b._emscripten_bind_Box_get_refcount_0=function(){return(Md=
b._emscripten_bind_Box_get_refcount_0=b.asm.Bb).apply(null,arguments)},Nd=b._emscripten_bind_Box___destroy___0=function(){return(Nd=b._emscripten_bind_Box___destroy___0=b.asm.Cb).apply(null,arguments)},Od=b._emscripten_bind_TessPDFRenderer_TessPDFRenderer_3=function(){return(Od=b._emscripten_bind_TessPDFRenderer_TessPDFRenderer_3=b.asm.Db).apply(null,arguments)},Pd=b._emscripten_bind_TessPDFRenderer_BeginDocument_1=function(){return(Pd=b._emscripten_bind_TessPDFRenderer_BeginDocument_1=b.asm.Eb).apply(null,
arguments)},Qd=b._emscripten_bind_TessPDFRenderer_AddImage_1=function(){return(Qd=b._emscripten_bind_TessPDFRenderer_AddImage_1=b.asm.Fb).apply(null,arguments)},Rd=b._emscripten_bind_TessPDFRenderer_EndDocument_0=function(){return(Rd=b._emscripten_bind_TessPDFRenderer_EndDocument_0=b.asm.Gb).apply(null,arguments)},Sd=b._emscripten_bind_TessPDFRenderer_happy_0=function(){return(Sd=b._emscripten_bind_TessPDFRenderer_happy_0=b.asm.Hb).apply(null,arguments)},Td=b._emscripten_bind_TessPDFRenderer_file_extension_0=
function(){return(Td=b._emscripten_bind_TessPDFRenderer_file_extension_0=b.asm.Ib).apply(null,arguments)},Ud=b._emscripten_bind_TessPDFRenderer_title_0=function(){return(Ud=b._emscripten_bind_TessPDFRenderer_title_0=b.asm.Jb).apply(null,arguments)},Vd=b._emscripten_bind_TessPDFRenderer_imagenum_0=function(){return(Vd=b._emscripten_bind_TessPDFRenderer_imagenum_0=b.asm.Kb).apply(null,arguments)},Wd=b._emscripten_bind_TessPDFRenderer___destroy___0=function(){return(Wd=b._emscripten_bind_TessPDFRenderer___destroy___0=
b.asm.Lb).apply(null,arguments)},Xd=b._emscripten_bind_PixaPtr___destroy___0=function(){return(Xd=b._emscripten_bind_PixaPtr___destroy___0=b.asm.Mb).apply(null,arguments)},Yd=b._emscripten_bind_FloatPtr___destroy___0=function(){return(Yd=b._emscripten_bind_FloatPtr___destroy___0=b.asm.Nb).apply(null,arguments)},Zd=b._emscripten_bind_ChoiceIterator_ChoiceIterator_1=function(){return(Zd=b._emscripten_bind_ChoiceIterator_ChoiceIterator_1=b.asm.Ob).apply(null,arguments)},$d=b._emscripten_bind_ChoiceIterator_Next_0=
function(){return($d=b._emscripten_bind_ChoiceIterator_Next_0=b.asm.Pb).apply(null,arguments)},ae=b._emscripten_bind_ChoiceIterator_GetUTF8Text_0=function(){return(ae=b._emscripten_bind_ChoiceIterator_GetUTF8Text_0=b.asm.Qb).apply(null,arguments)},be=b._emscripten_bind_ChoiceIterator_Confidence_0=function(){return(be=b._emscripten_bind_ChoiceIterator_Confidence_0=b.asm.Rb).apply(null,arguments)},ce=b._emscripten_bind_ChoiceIterator___destroy___0=function(){return(ce=b._emscripten_bind_ChoiceIterator___destroy___0=
b.asm.Sb).apply(null,arguments)},de=b._emscripten_bind_PixPtr___destroy___0=function(){return(de=b._emscripten_bind_PixPtr___destroy___0=b.asm.Tb).apply(null,arguments)},ee=b._emscripten_bind_UNICHARSET_get_script_from_script_id_1=function(){return(ee=b._emscripten_bind_UNICHARSET_get_script_from_script_id_1=b.asm.Ub).apply(null,arguments)},fe=b._emscripten_bind_UNICHARSET_get_script_id_from_name_1=function(){return(fe=b._emscripten_bind_UNICHARSET_get_script_id_from_name_1=b.asm.Vb).apply(null,arguments)},
ge=b._emscripten_bind_UNICHARSET_get_script_table_size_0=function(){return(ge=b._emscripten_bind_UNICHARSET_get_script_table_size_0=b.asm.Wb).apply(null,arguments)},he=b._emscripten_bind_UNICHARSET___destroy___0=function(){return(he=b._emscripten_bind_UNICHARSET___destroy___0=b.asm.Xb).apply(null,arguments)},ie=b._emscripten_bind_IntPtr___destroy___0=function(){return(ie=b._emscripten_bind_IntPtr___destroy___0=b.asm.Yb).apply(null,arguments)},je=b._emscripten_bind_Orientation___destroy___0=function(){return(je=
b._emscripten_bind_Orientation___destroy___0=b.asm.Zb).apply(null,arguments)},ke=b._emscripten_bind_OSBestResult_get_orientation_id_0=function(){return(ke=b._emscripten_bind_OSBestResult_get_orientation_id_0=b.asm._b).apply(null,arguments)},le=b._emscripten_bind_OSBestResult_get_script_id_0=function(){return(le=b._emscripten_bind_OSBestResult_get_script_id_0=b.asm.$b).apply(null,arguments)},me=b._emscripten_bind_OSBestResult_get_sconfidence_0=function(){return(me=b._emscripten_bind_OSBestResult_get_sconfidence_0=
b.asm.ac).apply(null,arguments)},ne=b._emscripten_bind_OSBestResult_get_oconfidence_0=function(){return(ne=b._emscripten_bind_OSBestResult_get_oconfidence_0=b.asm.bc).apply(null,arguments)},oe=b._emscripten_bind_OSBestResult___destroy___0=function(){return(oe=b._emscripten_bind_OSBestResult___destroy___0=b.asm.cc).apply(null,arguments)},pe=b._emscripten_bind_Boxa_get_n_0=function(){return(pe=b._emscripten_bind_Boxa_get_n_0=b.asm.dc).apply(null,arguments)},qe=b._emscripten_bind_Boxa_get_nalloc_0=function(){return(qe=
b._emscripten_bind_Boxa_get_nalloc_0=b.asm.ec).apply(null,arguments)},re=b._emscripten_bind_Boxa_get_refcount_0=function(){return(re=b._emscripten_bind_Boxa_get_refcount_0=b.asm.fc).apply(null,arguments)},se=b._emscripten_bind_Boxa_get_box_0=function(){return(se=b._emscripten_bind_Boxa_get_box_0=b.asm.gc).apply(null,arguments)},te=b._emscripten_bind_Boxa___destroy___0=function(){return(te=b._emscripten_bind_Boxa___destroy___0=b.asm.hc).apply(null,arguments)},ue=b._emscripten_bind_PixColormap_get_array_0=
function(){return(ue=b._emscripten_bind_PixColormap_get_array_0=b.asm.ic).apply(null,arguments)},ve=b._emscripten_bind_PixColormap_get_depth_0=function(){return(ve=b._emscripten_bind_PixColormap_get_depth_0=b.asm.jc).apply(null,arguments)},we=b._emscripten_bind_PixColormap_get_nalloc_0=function(){return(we=b._emscripten_bind_PixColormap_get_nalloc_0=b.asm.kc).apply(null,arguments)},xe=b._emscripten_bind_PixColormap_get_n_0=function(){return(xe=b._emscripten_bind_PixColormap_get_n_0=b.asm.lc).apply(null,
arguments)},ye=b._emscripten_bind_PixColormap___destroy___0=function(){return(ye=b._emscripten_bind_PixColormap___destroy___0=b.asm.mc).apply(null,arguments)},ze=b._emscripten_bind_Pta_get_n_0=function(){return(ze=b._emscripten_bind_Pta_get_n_0=b.asm.nc).apply(null,arguments)},Ae=b._emscripten_bind_Pta_get_nalloc_0=function(){return(Ae=b._emscripten_bind_Pta_get_nalloc_0=b.asm.oc).apply(null,arguments)},Be=b._emscripten_bind_Pta_get_refcount_0=function(){return(Be=b._emscripten_bind_Pta_get_refcount_0=
b.asm.pc).apply(null,arguments)},Ce=b._emscripten_bind_Pta_get_x_0=function(){return(Ce=b._emscripten_bind_Pta_get_x_0=b.asm.qc).apply(null,arguments)},De=b._emscripten_bind_Pta_get_y_0=function(){return(De=b._emscripten_bind_Pta_get_y_0=b.asm.rc).apply(null,arguments)},Ee=b._emscripten_bind_Pta___destroy___0=function(){return(Ee=b._emscripten_bind_Pta___destroy___0=b.asm.sc).apply(null,arguments)},Fe=b._emscripten_bind_Pix_get_w_0=function(){return(Fe=b._emscripten_bind_Pix_get_w_0=b.asm.tc).apply(null,
arguments)},Ge=b._emscripten_bind_Pix_get_h_0=function(){return(Ge=b._emscripten_bind_Pix_get_h_0=b.asm.uc).apply(null,arguments)},He=b._emscripten_bind_Pix_get_d_0=function(){return(He=b._emscripten_bind_Pix_get_d_0=b.asm.vc).apply(null,arguments)},Ie=b._emscripten_bind_Pix_get_spp_0=function(){return(Ie=b._emscripten_bind_Pix_get_spp_0=b.asm.wc).apply(null,arguments)},Je=b._emscripten_bind_Pix_get_wpl_0=function(){return(Je=b._emscripten_bind_Pix_get_wpl_0=b.asm.xc).apply(null,arguments)},Ke=b._emscripten_bind_Pix_get_refcount_0=
function(){return(Ke=b._emscripten_bind_Pix_get_refcount_0=b.asm.yc).apply(null,arguments)},Le=b._emscripten_bind_Pix_get_xres_0=function(){return(Le=b._emscripten_bind_Pix_get_xres_0=b.asm.zc).apply(null,arguments)},Me=b._emscripten_bind_Pix_get_yres_0=function(){return(Me=b._emscripten_bind_Pix_get_yres_0=b.asm.Ac).apply(null,arguments)},Ne=b._emscripten_bind_Pix_get_informat_0=function(){return(Ne=b._emscripten_bind_Pix_get_informat_0=b.asm.Bc).apply(null,arguments)},Oe=b._emscripten_bind_Pix_get_special_0=
function(){return(Oe=b._emscripten_bind_Pix_get_special_0=b.asm.Cc).apply(null,arguments)},Pe=b._emscripten_bind_Pix_get_text_0=function(){return(Pe=b._emscripten_bind_Pix_get_text_0=b.asm.Dc).apply(null,arguments)},Qe=b._emscripten_bind_Pix_get_colormap_0=function(){return(Qe=b._emscripten_bind_Pix_get_colormap_0=b.asm.Ec).apply(null,arguments)},Re=b._emscripten_bind_Pix_get_data_0=function(){return(Re=b._emscripten_bind_Pix_get_data_0=b.asm.Fc).apply(null,arguments)},Se=b._emscripten_bind_Pix___destroy___0=
function(){return(Se=b._emscripten_bind_Pix___destroy___0=b.asm.Gc).apply(null,arguments)},Te=b._emscripten_bind_DoublePtr___destroy___0=function(){return(Te=b._emscripten_bind_DoublePtr___destroy___0=b.asm.Hc).apply(null,arguments)},Ue=b._emscripten_bind_Dawg___destroy___0=function(){return(Ue=b._emscripten_bind_Dawg___destroy___0=b.asm.Ic).apply(null,arguments)},Ve=b._emscripten_bind_BoxPtr___destroy___0=function(){return(Ve=b._emscripten_bind_BoxPtr___destroy___0=b.asm.Jc).apply(null,arguments)},
We=b._emscripten_bind_TessBaseAPI_TessBaseAPI_0=function(){return(We=b._emscripten_bind_TessBaseAPI_TessBaseAPI_0=b.asm.Kc).apply(null,arguments)},Xe=b._emscripten_bind_TessBaseAPI_Version_0=function(){return(Xe=b._emscripten_bind_TessBaseAPI_Version_0=b.asm.Lc).apply(null,arguments)},Ye=b._emscripten_bind_TessBaseAPI_SetInputName_1=function(){return(Ye=b._emscripten_bind_TessBaseAPI_SetInputName_1=b.asm.Mc).apply(null,arguments)},Ze=b._emscripten_bind_TessBaseAPI_GetInputName_0=function(){return(Ze=
b._emscripten_bind_TessBaseAPI_GetInputName_0=b.asm.Nc).apply(null,arguments)},$e=b._emscripten_bind_TessBaseAPI_SetInputImage_1=function(){return($e=b._emscripten_bind_TessBaseAPI_SetInputImage_1=b.asm.Oc).apply(null,arguments)},af=b._emscripten_bind_TessBaseAPI_GetInputImage_0=function(){return(af=b._emscripten_bind_TessBaseAPI_GetInputImage_0=b.asm.Pc).apply(null,arguments)},bf=b._emscripten_bind_TessBaseAPI_GetSourceYResolution_0=function(){return(bf=b._emscripten_bind_TessBaseAPI_GetSourceYResolution_0=
b.asm.Qc).apply(null,arguments)},cf=b._emscripten_bind_TessBaseAPI_GetDatapath_0=function(){return(cf=b._emscripten_bind_TessBaseAPI_GetDatapath_0=b.asm.Rc).apply(null,arguments)},df=b._emscripten_bind_TessBaseAPI_SetOutputName_1=function(){return(df=b._emscripten_bind_TessBaseAPI_SetOutputName_1=b.asm.Sc).apply(null,arguments)},ef=b._emscripten_bind_TessBaseAPI_SetVariable_2=function(){return(ef=b._emscripten_bind_TessBaseAPI_SetVariable_2=b.asm.Tc).apply(null,arguments)},ff=b._emscripten_bind_TessBaseAPI_SetDebugVariable_2=
function(){return(ff=b._emscripten_bind_TessBaseAPI_SetDebugVariable_2=b.asm.Uc).apply(null,arguments)},gf=b._emscripten_bind_TessBaseAPI_GetIntVariable_2=function(){return(gf=b._emscripten_bind_TessBaseAPI_GetIntVariable_2=b.asm.Vc).apply(null,arguments)},hf=b._emscripten_bind_TessBaseAPI_GetBoolVariable_2=function(){return(hf=b._emscripten_bind_TessBaseAPI_GetBoolVariable_2=b.asm.Wc).apply(null,arguments)},jf=b._emscripten_bind_TessBaseAPI_GetDoubleVariable_2=function(){return(jf=b._emscripten_bind_TessBaseAPI_GetDoubleVariable_2=
b.asm.Xc).apply(null,arguments)},kf=b._emscripten_bind_TessBaseAPI_GetStringVariable_1=function(){return(kf=b._emscripten_bind_TessBaseAPI_GetStringVariable_1=b.asm.Yc).apply(null,arguments)},lf=b._emscripten_bind_TessBaseAPI_SaveParameters_1=function(){return(lf=b._emscripten_bind_TessBaseAPI_SaveParameters_1=b.asm.Zc).apply(null,arguments)},mf=b._emscripten_bind_TessBaseAPI_RestoreParameters_1=function(){return(mf=b._emscripten_bind_TessBaseAPI_RestoreParameters_1=b.asm._c).apply(null,arguments)},
nf=b._emscripten_bind_TessBaseAPI_Init_2=function(){return(nf=b._emscripten_bind_TessBaseAPI_Init_2=b.asm.$c).apply(null,arguments)},of=b._emscripten_bind_TessBaseAPI_Init_3=function(){return(of=b._emscripten_bind_TessBaseAPI_Init_3=b.asm.ad).apply(null,arguments)},pf=b._emscripten_bind_TessBaseAPI_Init_4=function(){return(pf=b._emscripten_bind_TessBaseAPI_Init_4=b.asm.bd).apply(null,arguments)},qf=b._emscripten_bind_TessBaseAPI_GetInitLanguagesAsString_0=function(){return(qf=b._emscripten_bind_TessBaseAPI_GetInitLanguagesAsString_0=
b.asm.cd).apply(null,arguments)},rf=b._emscripten_bind_TessBaseAPI_InitForAnalysePage_0=function(){return(rf=b._emscripten_bind_TessBaseAPI_InitForAnalysePage_0=b.asm.dd).apply(null,arguments)},sf=b._emscripten_bind_TessBaseAPI_ReadConfigFile_1=function(){return(sf=b._emscripten_bind_TessBaseAPI_ReadConfigFile_1=b.asm.ed).apply(null,arguments)},tf=b._emscripten_bind_TessBaseAPI_ReadDebugConfigFile_1=function(){return(tf=b._emscripten_bind_TessBaseAPI_ReadDebugConfigFile_1=b.asm.fd).apply(null,arguments)},
uf=b._emscripten_bind_TessBaseAPI_SetPageSegMode_1=function(){return(uf=b._emscripten_bind_TessBaseAPI_SetPageSegMode_1=b.asm.gd).apply(null,arguments)},vf=b._emscripten_bind_TessBaseAPI_GetPageSegMode_0=function(){return(vf=b._emscripten_bind_TessBaseAPI_GetPageSegMode_0=b.asm.hd).apply(null,arguments)},wf=b._emscripten_bind_TessBaseAPI_TesseractRect_7=function(){return(wf=b._emscripten_bind_TessBaseAPI_TesseractRect_7=b.asm.id).apply(null,arguments)},xf=b._emscripten_bind_TessBaseAPI_ClearAdaptiveClassifier_0=
function(){return(xf=b._emscripten_bind_TessBaseAPI_ClearAdaptiveClassifier_0=b.asm.jd).apply(null,arguments)},yf=b._emscripten_bind_TessBaseAPI_SetImage_1=function(){return(yf=b._emscripten_bind_TessBaseAPI_SetImage_1=b.asm.kd).apply(null,arguments)},zf=b._emscripten_bind_TessBaseAPI_SetImage_5=function(){return(zf=b._emscripten_bind_TessBaseAPI_SetImage_5=b.asm.ld).apply(null,arguments)},Af=b._emscripten_bind_TessBaseAPI_SetImageFile_1=function(){return(Af=b._emscripten_bind_TessBaseAPI_SetImageFile_1=
b.asm.md).apply(null,arguments)},Bf=b._emscripten_bind_TessBaseAPI_SetSourceResolution_1=function(){return(Bf=b._emscripten_bind_TessBaseAPI_SetSourceResolution_1=b.asm.nd).apply(null,arguments)},Cf=b._emscripten_bind_TessBaseAPI_SetRectangle_4=function(){return(Cf=b._emscripten_bind_TessBaseAPI_SetRectangle_4=b.asm.od).apply(null,arguments)},Df=b._emscripten_bind_TessBaseAPI_GetThresholdedImage_0=function(){return(Df=b._emscripten_bind_TessBaseAPI_GetThresholdedImage_0=b.asm.pd).apply(null,arguments)},
Ef=b._emscripten_bind_TessBaseAPI_WriteImage_0=function(){return(Ef=b._emscripten_bind_TessBaseAPI_WriteImage_0=b.asm.qd).apply(null,arguments)},Ff=b._emscripten_bind_TessBaseAPI_FindLines_0=function(){return(Ff=b._emscripten_bind_TessBaseAPI_FindLines_0=b.asm.rd).apply(null,arguments)},Gf=b._emscripten_bind_TessBaseAPI_GetGradient_0=function(){return(Gf=b._emscripten_bind_TessBaseAPI_GetGradient_0=b.asm.sd).apply(null,arguments)},Hf=b._emscripten_bind_TessBaseAPI_GetRegions_1=function(){return(Hf=
b._emscripten_bind_TessBaseAPI_GetRegions_1=b.asm.td).apply(null,arguments)},If=b._emscripten_bind_TessBaseAPI_GetTextlines_2=function(){return(If=b._emscripten_bind_TessBaseAPI_GetTextlines_2=b.asm.ud).apply(null,arguments)},Jf=b._emscripten_bind_TessBaseAPI_GetTextlines_5=function(){return(Jf=b._emscripten_bind_TessBaseAPI_GetTextlines_5=b.asm.vd).apply(null,arguments)},Kf=b._emscripten_bind_TessBaseAPI_GetStrips_2=function(){return(Kf=b._emscripten_bind_TessBaseAPI_GetStrips_2=b.asm.wd).apply(null,
arguments)},Lf=b._emscripten_bind_TessBaseAPI_GetWords_1=function(){return(Lf=b._emscripten_bind_TessBaseAPI_GetWords_1=b.asm.xd).apply(null,arguments)},Mf=b._emscripten_bind_TessBaseAPI_GetConnectedComponents_1=function(){return(Mf=b._emscripten_bind_TessBaseAPI_GetConnectedComponents_1=b.asm.yd).apply(null,arguments)},Nf=b._emscripten_bind_TessBaseAPI_GetComponentImages_4=function(){return(Nf=b._emscripten_bind_TessBaseAPI_GetComponentImages_4=b.asm.zd).apply(null,arguments)},Of=b._emscripten_bind_TessBaseAPI_GetComponentImages_7=
function(){return(Of=b._emscripten_bind_TessBaseAPI_GetComponentImages_7=b.asm.Ad).apply(null,arguments)},Pf=b._emscripten_bind_TessBaseAPI_GetThresholdedImageScaleFactor_0=function(){return(Pf=b._emscripten_bind_TessBaseAPI_GetThresholdedImageScaleFactor_0=b.asm.Bd).apply(null,arguments)},Qf=b._emscripten_bind_TessBaseAPI_AnalyseLayout_0=function(){return(Qf=b._emscripten_bind_TessBaseAPI_AnalyseLayout_0=b.asm.Cd).apply(null,arguments)},Rf=b._emscripten_bind_TessBaseAPI_AnalyseLayout_1=function(){return(Rf=
b._emscripten_bind_TessBaseAPI_AnalyseLayout_1=b.asm.Dd).apply(null,arguments)},Sf=b._emscripten_bind_TessBaseAPI_Recognize_1=function(){return(Sf=b._emscripten_bind_TessBaseAPI_Recognize_1=b.asm.Ed).apply(null,arguments)},Tf=b._emscripten_bind_TessBaseAPI_ProcessPages_4=function(){return(Tf=b._emscripten_bind_TessBaseAPI_ProcessPages_4=b.asm.Fd).apply(null,arguments)},Uf=b._emscripten_bind_TessBaseAPI_ProcessPage_6=function(){return(Uf=b._emscripten_bind_TessBaseAPI_ProcessPage_6=b.asm.Gd).apply(null,
arguments)},Vf=b._emscripten_bind_TessBaseAPI_GetIterator_0=function(){return(Vf=b._emscripten_bind_TessBaseAPI_GetIterator_0=b.asm.Hd).apply(null,arguments)},Wf=b._emscripten_bind_TessBaseAPI_GetUTF8Text_0=function(){return(Wf=b._emscripten_bind_TessBaseAPI_GetUTF8Text_0=b.asm.Id).apply(null,arguments)},Xf=b._emscripten_bind_TessBaseAPI_GetHOCRText_1=function(){return(Xf=b._emscripten_bind_TessBaseAPI_GetHOCRText_1=b.asm.Jd).apply(null,arguments)},Yf=b._emscripten_bind_TessBaseAPI_GetTSVText_1=function(){return(Yf=
b._emscripten_bind_TessBaseAPI_GetTSVText_1=b.asm.Kd).apply(null,arguments)},Zf=b._emscripten_bind_TessBaseAPI_GetBoxText_1=function(){return(Zf=b._emscripten_bind_TessBaseAPI_GetBoxText_1=b.asm.Ld).apply(null,arguments)},$f=b._emscripten_bind_TessBaseAPI_GetUNLVText_0=function(){return($f=b._emscripten_bind_TessBaseAPI_GetUNLVText_0=b.asm.Md).apply(null,arguments)},ag=b._emscripten_bind_TessBaseAPI_GetOsdText_1=function(){return(ag=b._emscripten_bind_TessBaseAPI_GetOsdText_1=b.asm.Nd).apply(null,
arguments)},bg=b._emscripten_bind_TessBaseAPI_MeanTextConf_0=function(){return(bg=b._emscripten_bind_TessBaseAPI_MeanTextConf_0=b.asm.Od).apply(null,arguments)},cg=b._emscripten_bind_TessBaseAPI_AllWordConfidences_0=function(){return(cg=b._emscripten_bind_TessBaseAPI_AllWordConfidences_0=b.asm.Pd).apply(null,arguments)},dg=b._emscripten_bind_TessBaseAPI_Clear_0=function(){return(dg=b._emscripten_bind_TessBaseAPI_Clear_0=b.asm.Qd).apply(null,arguments)},eg=b._emscripten_bind_TessBaseAPI_End_0=function(){return(eg=
b._emscripten_bind_TessBaseAPI_End_0=b.asm.Rd).apply(null,arguments)},fg=b._emscripten_bind_TessBaseAPI_ClearPersistentCache_0=function(){return(fg=b._emscripten_bind_TessBaseAPI_ClearPersistentCache_0=b.asm.Sd).apply(null,arguments)},gg=b._emscripten_bind_TessBaseAPI_IsValidWord_1=function(){return(gg=b._emscripten_bind_TessBaseAPI_IsValidWord_1=b.asm.Td).apply(null,arguments)},hg=b._emscripten_bind_TessBaseAPI_IsValidCharacter_1=function(){return(hg=b._emscripten_bind_TessBaseAPI_IsValidCharacter_1=
b.asm.Ud).apply(null,arguments)},ig=b._emscripten_bind_TessBaseAPI_DetectOS_1=function(){return(ig=b._emscripten_bind_TessBaseAPI_DetectOS_1=b.asm.Vd).apply(null,arguments)},jg=b._emscripten_bind_TessBaseAPI_GetUnichar_1=function(){return(jg=b._emscripten_bind_TessBaseAPI_GetUnichar_1=b.asm.Wd).apply(null,arguments)},kg=b._emscripten_bind_TessBaseAPI_GetDawg_1=function(){return(kg=b._emscripten_bind_TessBaseAPI_GetDawg_1=b.asm.Xd).apply(null,arguments)},lg=b._emscripten_bind_TessBaseAPI_NumDawgs_0=
function(){return(lg=b._emscripten_bind_TessBaseAPI_NumDawgs_0=b.asm.Yd).apply(null,arguments)},mg=b._emscripten_bind_TessBaseAPI_oem_0=function(){return(mg=b._emscripten_bind_TessBaseAPI_oem_0=b.asm.Zd).apply(null,arguments)},ng=b._emscripten_bind_TessBaseAPI___destroy___0=function(){return(ng=b._emscripten_bind_TessBaseAPI___destroy___0=b.asm._d).apply(null,arguments)},og=b._emscripten_bind_OSResults_OSResults_0=function(){return(og=b._emscripten_bind_OSResults_OSResults_0=b.asm.$d).apply(null,
arguments)},pg=b._emscripten_bind_OSResults_print_scores_0=function(){return(pg=b._emscripten_bind_OSResults_print_scores_0=b.asm.ae).apply(null,arguments)},qg=b._emscripten_bind_OSResults_get_best_result_0=function(){return(qg=b._emscripten_bind_OSResults_get_best_result_0=b.asm.be).apply(null,arguments)},rg=b._emscripten_bind_OSResults_get_unicharset_0=function(){return(rg=b._emscripten_bind_OSResults_get_unicharset_0=b.asm.ce).apply(null,arguments)},sg=b._emscripten_bind_OSResults___destroy___0=
function(){return(sg=b._emscripten_bind_OSResults___destroy___0=b.asm.de).apply(null,arguments)},tg=b._emscripten_bind_Pixa_get_n_0=function(){return(tg=b._emscripten_bind_Pixa_get_n_0=b.asm.ee).apply(null,arguments)},ug=b._emscripten_bind_Pixa_get_nalloc_0=function(){return(ug=b._emscripten_bind_Pixa_get_nalloc_0=b.asm.fe).apply(null,arguments)},vg=b._emscripten_bind_Pixa_get_refcount_0=function(){return(vg=b._emscripten_bind_Pixa_get_refcount_0=b.asm.ge).apply(null,arguments)},wg=b._emscripten_bind_Pixa_get_pix_0=
function(){return(wg=b._emscripten_bind_Pixa_get_pix_0=b.asm.he).apply(null,arguments)},xg=b._emscripten_bind_Pixa_get_boxa_0=function(){return(xg=b._emscripten_bind_Pixa_get_boxa_0=b.asm.ie).apply(null,arguments)},yg=b._emscripten_bind_Pixa___destroy___0=function(){return(yg=b._emscripten_bind_Pixa___destroy___0=b.asm.je).apply(null,arguments)},zg=b._emscripten_enum_PageIteratorLevel_RIL_BLOCK=function(){return(zg=b._emscripten_enum_PageIteratorLevel_RIL_BLOCK=b.asm.ke).apply(null,arguments)},Ag=
b._emscripten_enum_PageIteratorLevel_RIL_PARA=function(){return(Ag=b._emscripten_enum_PageIteratorLevel_RIL_PARA=b.asm.le).apply(null,arguments)},Bg=b._emscripten_enum_PageIteratorLevel_RIL_TEXTLINE=function(){return(Bg=b._emscripten_enum_PageIteratorLevel_RIL_TEXTLINE=b.asm.me).apply(null,arguments)},Cg=b._emscripten_enum_PageIteratorLevel_RIL_WORD=function(){return(Cg=b._emscripten_enum_PageIteratorLevel_RIL_WORD=b.asm.ne).apply(null,arguments)},Dg=b._emscripten_enum_PageIteratorLevel_RIL_SYMBOL=
function(){return(Dg=b._emscripten_enum_PageIteratorLevel_RIL_SYMBOL=b.asm.oe).apply(null,arguments)},Eg=b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_ONLY=function(){return(Eg=b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_ONLY=b.asm.pe).apply(null,arguments)},Fg=b._emscripten_enum_OcrEngineMode_OEM_LSTM_ONLY=function(){return(Fg=b._emscripten_enum_OcrEngineMode_OEM_LSTM_ONLY=b.asm.qe).apply(null,arguments)},Gg=b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_LSTM_COMBINED=function(){return(Gg=b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_LSTM_COMBINED=
b.asm.re).apply(null,arguments)},Hg=b._emscripten_enum_OcrEngineMode_OEM_DEFAULT=function(){return(Hg=b._emscripten_enum_OcrEngineMode_OEM_DEFAULT=b.asm.se).apply(null,arguments)},Ig=b._emscripten_enum_OcrEngineMode_OEM_COUNT=function(){return(Ig=b._emscripten_enum_OcrEngineMode_OEM_COUNT=b.asm.te).apply(null,arguments)},Jg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_LEFT_TO_RIGHT=function(){return(Jg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_LEFT_TO_RIGHT=b.asm.ue).apply(null,
arguments)},Kg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_RIGHT_TO_LEFT=function(){return(Kg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_RIGHT_TO_LEFT=b.asm.ve).apply(null,arguments)},Lg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_TOP_TO_BOTTOM=function(){return(Lg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_TOP_TO_BOTTOM=b.asm.we).apply(null,arguments)},Mg=b._emscripten_enum_PolyBlockType_PT_UNKNOWN=function(){return(Mg=b._emscripten_enum_PolyBlockType_PT_UNKNOWN=
b.asm.xe).apply(null,arguments)},Ng=b._emscripten_enum_PolyBlockType_PT_FLOWING_TEXT=function(){return(Ng=b._emscripten_enum_PolyBlockType_PT_FLOWING_TEXT=b.asm.ye).apply(null,arguments)},Og=b._emscripten_enum_PolyBlockType_PT_HEADING_TEXT=function(){return(Og=b._emscripten_enum_PolyBlockType_PT_HEADING_TEXT=b.asm.ze).apply(null,arguments)},Pg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_TEXT=function(){return(Pg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_TEXT=b.asm.Ae).apply(null,arguments)},Qg=
b._emscripten_enum_PolyBlockType_PT_EQUATION=function(){return(Qg=b._emscripten_enum_PolyBlockType_PT_EQUATION=b.asm.Be).apply(null,arguments)},Rg=b._emscripten_enum_PolyBlockType_PT_INLINE_EQUATION=function(){return(Rg=b._emscripten_enum_PolyBlockType_PT_INLINE_EQUATION=b.asm.Ce).apply(null,arguments)},Sg=b._emscripten_enum_PolyBlockType_PT_TABLE=function(){return(Sg=b._emscripten_enum_PolyBlockType_PT_TABLE=b.asm.De).apply(null,arguments)},Tg=b._emscripten_enum_PolyBlockType_PT_VERTICAL_TEXT=function(){return(Tg=
b._emscripten_enum_PolyBlockType_PT_VERTICAL_TEXT=b.asm.Ee).apply(null,arguments)},Ug=b._emscripten_enum_PolyBlockType_PT_CAPTION_TEXT=function(){return(Ug=b._emscripten_enum_PolyBlockType_PT_CAPTION_TEXT=b.asm.Fe).apply(null,arguments)},Vg=b._emscripten_enum_PolyBlockType_PT_FLOWING_IMAGE=function(){return(Vg=b._emscripten_enum_PolyBlockType_PT_FLOWING_IMAGE=b.asm.Ge).apply(null,arguments)},Wg=b._emscripten_enum_PolyBlockType_PT_HEADING_IMAGE=function(){return(Wg=b._emscripten_enum_PolyBlockType_PT_HEADING_IMAGE=
b.asm.He).apply(null,arguments)},Xg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_IMAGE=function(){return(Xg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_IMAGE=b.asm.Ie).apply(null,arguments)},Yg=b._emscripten_enum_PolyBlockType_PT_HORZ_LINE=function(){return(Yg=b._emscripten_enum_PolyBlockType_PT_HORZ_LINE=b.asm.Je).apply(null,arguments)},Zg=b._emscripten_enum_PolyBlockType_PT_VERT_LINE=function(){return(Zg=b._emscripten_enum_PolyBlockType_PT_VERT_LINE=b.asm.Ke).apply(null,arguments)},$g=b._emscripten_enum_PolyBlockType_PT_NOISE=
function(){return($g=b._emscripten_enum_PolyBlockType_PT_NOISE=b.asm.Le).apply(null,arguments)},ah=b._emscripten_enum_PolyBlockType_PT_COUNT=function(){return(ah=b._emscripten_enum_PolyBlockType_PT_COUNT=b.asm.Me).apply(null,arguments)},bh=b._emscripten_enum_StrongScriptDirection_DIR_NEUTRAL=function(){return(bh=b._emscripten_enum_StrongScriptDirection_DIR_NEUTRAL=b.asm.Ne).apply(null,arguments)},ch=b._emscripten_enum_StrongScriptDirection_DIR_LEFT_TO_RIGHT=function(){return(ch=b._emscripten_enum_StrongScriptDirection_DIR_LEFT_TO_RIGHT=
b.asm.Oe).apply(null,arguments)},dh=b._emscripten_enum_StrongScriptDirection_DIR_RIGHT_TO_LEFT=function(){return(dh=b._emscripten_enum_StrongScriptDirection_DIR_RIGHT_TO_LEFT=b.asm.Pe).apply(null,arguments)},eh=b._emscripten_enum_StrongScriptDirection_DIR_MIX=function(){return(eh=b._emscripten_enum_StrongScriptDirection_DIR_MIX=b.asm.Qe).apply(null,arguments)},fh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_UNKNOWN=function(){return(fh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_UNKNOWN=
b.asm.Re).apply(null,arguments)},gh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_LEFT=function(){return(gh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_LEFT=b.asm.Se).apply(null,arguments)},hh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_CENTER=function(){return(hh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_CENTER=b.asm.Te).apply(null,arguments)},ih=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_RIGHT=function(){return(ih=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_RIGHT=
b.asm.Ue).apply(null,arguments)},jh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_LEFT_TO_RIGHT=function(){return(jh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_LEFT_TO_RIGHT=b.asm.Ve).apply(null,arguments)},kh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_RIGHT_TO_LEFT=function(){return(kh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_RIGHT_TO_LEFT=b.asm.We).apply(null,arguments)},lh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_TOP_TO_BOTTOM=function(){return(lh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_TOP_TO_BOTTOM=
b.asm.Xe).apply(null,arguments)},mh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_UP=function(){return(mh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_UP=b.asm.Ye).apply(null,arguments)},nh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_RIGHT=function(){return(nh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_RIGHT=b.asm.Ze).apply(null,arguments)},oh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_DOWN=function(){return(oh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_DOWN=b.asm._e).apply(null,
arguments)},ph=b._emscripten_enum_Orientation__ORIENTATION_PAGE_LEFT=function(){return(ph=b._emscripten_enum_Orientation__ORIENTATION_PAGE_LEFT=b.asm.$e).apply(null,arguments)},qh=b._emscripten_enum_PageSegMode_PSM_OSD_ONLY=function(){return(qh=b._emscripten_enum_PageSegMode_PSM_OSD_ONLY=b.asm.af).apply(null,arguments)},rh=b._emscripten_enum_PageSegMode_PSM_AUTO_OSD=function(){return(rh=b._emscripten_enum_PageSegMode_PSM_AUTO_OSD=b.asm.bf).apply(null,arguments)},sh=b._emscripten_enum_PageSegMode_PSM_AUTO_ONLY=
function(){return(sh=b._emscripten_enum_PageSegMode_PSM_AUTO_ONLY=b.asm.cf).apply(null,arguments)},th=b._emscripten_enum_PageSegMode_PSM_AUTO=function(){return(th=b._emscripten_enum_PageSegMode_PSM_AUTO=b.asm.df).apply(null,arguments)},uh=b._emscripten_enum_PageSegMode_PSM_SINGLE_COLUMN=function(){return(uh=b._emscripten_enum_PageSegMode_PSM_SINGLE_COLUMN=b.asm.ef).apply(null,arguments)},vh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK_VERT_TEXT=function(){return(vh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK_VERT_TEXT=
b.asm.ff).apply(null,arguments)},wh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK=function(){return(wh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK=b.asm.gf).apply(null,arguments)},xh=b._emscripten_enum_PageSegMode_PSM_SINGLE_LINE=function(){return(xh=b._emscripten_enum_PageSegMode_PSM_SINGLE_LINE=b.asm.hf).apply(null,arguments)},yh=b._emscripten_enum_PageSegMode_PSM_SINGLE_WORD=function(){return(yh=b._emscripten_enum_PageSegMode_PSM_SINGLE_WORD=b.asm.jf).apply(null,arguments)},zh=b._emscripten_enum_PageSegMode_PSM_CIRCLE_WORD=
function(){return(zh=b._emscripten_enum_PageSegMode_PSM_CIRCLE_WORD=b.asm.kf).apply(null,arguments)},Ah=b._emscripten_enum_PageSegMode_PSM_SINGLE_CHAR=function(){return(Ah=b._emscripten_enum_PageSegMode_PSM_SINGLE_CHAR=b.asm.lf).apply(null,arguments)},Bh=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT=function(){return(Bh=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT=b.asm.mf).apply(null,arguments)},Ch=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT_OSD=function(){return(Ch=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT_OSD=
b.asm.nf).apply(null,arguments)},Dh=b._emscripten_enum_PageSegMode_PSM_RAW_LINE=function(){return(Dh=b._emscripten_enum_PageSegMode_PSM_RAW_LINE=b.asm.of).apply(null,arguments)},Eh=b._emscripten_enum_PageSegMode_PSM_COUNT=function(){return(Eh=b._emscripten_enum_PageSegMode_PSM_COUNT=b.asm.pf).apply(null,arguments)};b._pixDestroy=function(){return(b._pixDestroy=b.asm.rf).apply(null,arguments)};b._ptaDestroy=function(){return(b._ptaDestroy=b.asm.sf).apply(null,arguments)};
b._pixaDestroy=function(){return(b._pixaDestroy=b.asm.tf).apply(null,arguments)};b._boxaDestroy=function(){return(b._boxaDestroy=b.asm.uf).apply(null,arguments)};b._pixReadMem=function(){return(b._pixReadMem=b.asm.vf).apply(null,arguments)};function Rb(){return(Rb=b.asm.wf).apply(null,arguments)}var Fh=b._free=function(){return(Fh=b._free=b.asm.xf).apply(null,arguments)},Fb=b._malloc=function(){return(Fb=b._malloc=b.asm.yf).apply(null,arguments)};
b._pixReadHeaderMem=function(){return(b._pixReadHeaderMem=b.asm.zf).apply(null,arguments)};function C(){return(C=b.asm.Af).apply(null,arguments)}function D(){return(D=b.asm.Bf).apply(null,arguments)}b.___emscripten_embedded_file_data=601504;function Ub(a,c,d,e){var g=C();try{return Pb(a)(c,d,e)}catch(h){D(g);if(h!==h+0)throw h;zb()}}function Xb(a,c){var d=C();try{Pb(a)(c)}catch(e){D(d);if(e!==e+0)throw e;zb()}}function Sb(a,c){var d=C();try{return Pb(a)(c)}catch(e){D(d);if(e!==e+0)throw e;zb()}}
function Zb(a,c,d,e){var g=C();try{Pb(a)(c,d,e)}catch(h){D(g);if(h!==h+0)throw h;zb()}}function Yb(a,c,d){var e=C();try{Pb(a)(c,d)}catch(g){D(e);if(g!==g+0)throw g;zb()}}function Tb(a,c,d){var e=C();try{return Pb(a)(c,d)}catch(g){D(e);if(g!==g+0)throw g;zb()}}function Vb(a,c,d,e,g){var h=C();try{return Pb(a)(c,d,e,g)}catch(k){D(h);if(k!==k+0)throw k;zb()}}function $b(a,c,d,e,g){var h=C();try{Pb(a)(c,d,e,g)}catch(k){D(h);if(k!==k+0)throw k;zb()}}
function Wb(a,c,d,e,g,h){var k=C();try{return Pb(a)(c,d,e,g,h)}catch(m){D(k);if(m!==m+0)throw m;zb()}}function bc(a,c,d,e,g,h,k,m,v,q){var t=C();try{Pb(a)(c,d,e,g,h,k,m,v,q)}catch(F){D(t);if(F!==F+0)throw F;zb()}}function ac(a,c,d,e,g,h){var k=C();try{Pb(a)(c,d,e,g,h)}catch(m){D(k);if(m!==m+0)throw m;zb()}}b.addRunDependency=Ha;b.removeRunDependency=Ia;b.FS_createPath=A.Dg;b.FS_createDataFile=A.ug;b.FS_createLazyFile=A.ah;b.FS_createDevice=A.Qf;b.FS_unlink=A.unlink;b.setValue=Ya;b.getValue=Xa;
b.FS_createPreloadedFile=A.bh;b.FS=A;var Gh;Ga=function Hh(){Gh||Ih();Gh||(Ga=Hh)};
function Ih(){function a(){if(!Gh&&(Gh=!0,b.calledRun=!0,!sa)){Ca=!0;b.noFSInit||A.cg.Og||A.cg();A.oh=!1;Sa(Aa);aa(b);if(b.onRuntimeInitialized)b.onRuntimeInitialized();if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var c=b.postRun.shift();Ba.unshift(c)}Sa(Ba)}}if(!(0<Ea)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)Da();Sa(za);0<Ea||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},
1);a()},1)):a())}}if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();Ih();function G(){}G.prototype=Object.create(G.prototype);G.prototype.constructor=G;G.prototype.Hf=G;G.If={};b.WrapperObject=G;function Jh(a){return(a||G).If}b.getCache=Jh;function H(a,c){var d=Jh(c),e=d[a];if(e)return e;e=Object.create((c||G).prototype);e.Cf=a;return d[a]=e}b.wrapPointer=H;b.castObject=function(a,c){return H(a.Cf,c)};b.NULL=H(0);
b.destroy=function(a){if(!a.__destroy__)throw"Error: Cannot destroy object. (Did you create it yourself?)";a.__destroy__();delete Jh(a.Hf)[a.Cf]};b.compare=function(a,c){return a.Cf===c.Cf};b.getPointer=function(a){return a.Cf};b.getClass=function(a){return a.Hf};var Kh=0,Lh=0,Mh=0,Nh=[],Oh=0;function I(){if(Oh){for(var a=0;a<Nh.length;a++)b._free(Nh[a]);Nh.length=0;b._free(Kh);Kh=0;Lh+=Oh;Oh=0}Kh||(Lh+=128,(Kh=b._malloc(Lh))||n());Mh=0}
function J(a){if("string"===typeof a){a=kb(a);var c=p;Kh||n();c=a.length*c.BYTES_PER_ELEMENT;c=c+7&-8;if(Mh+c>=Lh){0<c||n();Oh+=c;var d=b._malloc(c);Nh.push(d)}else d=Kh+Mh,Mh+=c;c=d;d=p;var e=c;switch(d.BYTES_PER_ELEMENT){case 2:e>>=1;break;case 4:e>>=2;break;case 8:e>>=3}for(var g=0;g<a.length;g++)d[e+g]=a[g];return c}return a}function Ph(){throw"cannot construct a ParagraphJustification, no constructor in IDL";}Ph.prototype=Object.create(G.prototype);Ph.prototype.constructor=Ph;
Ph.prototype.Hf=Ph;Ph.If={};b.ParagraphJustification=Ph;Ph.prototype.__destroy__=function(){dc(this.Cf)};function Qh(){throw"cannot construct a BoolPtr, no constructor in IDL";}Qh.prototype=Object.create(G.prototype);Qh.prototype.constructor=Qh;Qh.prototype.Hf=Qh;Qh.If={};b.BoolPtr=Qh;Qh.prototype.__destroy__=function(){ec(this.Cf)};function K(){throw"cannot construct a TessResultRenderer, no constructor in IDL";}K.prototype=Object.create(G.prototype);K.prototype.constructor=K;K.prototype.Hf=K;
K.If={};b.TessResultRenderer=K;K.prototype.BeginDocument=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);return!!fc(c,a)};K.prototype.AddImage=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return!!gc(c,a)};K.prototype.EndDocument=function(){return!!hc(this.Cf)};K.prototype.happy=function(){return!!ic(this.Cf)};K.prototype.file_extension=function(){return z(jc(this.Cf))};K.prototype.title=K.prototype.title=function(){return z(kc(this.Cf))};K.prototype.imagenum=function(){return lc(this.Cf)};
K.prototype.__destroy__=function(){mc(this.Cf)};function Rh(){throw"cannot construct a LongStarPtr, no constructor in IDL";}Rh.prototype=Object.create(G.prototype);Rh.prototype.constructor=Rh;Rh.prototype.Hf=Rh;Rh.If={};b.LongStarPtr=Rh;Rh.prototype.__destroy__=function(){nc(this.Cf)};function Sh(){throw"cannot construct a VoidPtr, no constructor in IDL";}Sh.prototype=Object.create(G.prototype);Sh.prototype.constructor=Sh;Sh.prototype.Hf=Sh;Sh.If={};b.VoidPtr=Sh;Sh.prototype.__destroy__=function(){oc(this.Cf)};
function L(a){a&&"object"===typeof a&&(a=a.Cf);this.Cf=pc(a);Jh(L)[this.Cf]=this}L.prototype=Object.create(G.prototype);L.prototype.constructor=L;L.prototype.Hf=L;L.If={};b.ResultIterator=L;L.prototype.Begin=function(){qc(this.Cf)};L.prototype.RestartParagraph=function(){rc(this.Cf)};L.prototype.IsWithinFirstTextlineOfParagraph=function(){return!!sc(this.Cf)};L.prototype.RestartRow=function(){tc(this.Cf)};L.prototype.Next=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return!!uc(c,a)};
L.prototype.IsAtBeginningOf=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return!!vc(c,a)};L.prototype.IsAtFinalElement=function(a,c){var d=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);return!!wc(d,a,c)};L.prototype.Cmp=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return xc(c,a)};L.prototype.SetBoundingBoxComponents=function(a,c){var d=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);yc(d,a,c)};
L.prototype.BoundingBox=function(a,c,d,e,g,h){var k=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);h&&"object"===typeof h&&(h=h.Cf);return void 0===h?!!zc(k,a,c,d,e,g):!!Ac(k,a,c,d,e,g,h)};
L.prototype.BoundingBoxInternal=function(a,c,d,e,g){var h=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);return!!Bc(h,a,c,d,e,g)};L.prototype.Empty=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return!!Cc(c,a)};L.prototype.BlockType=function(){return Dc(this.Cf)};L.prototype.BlockPolygon=function(){return H(Ec(this.Cf),M)};
L.prototype.GetBinaryImage=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return H(Fc(c,a),N)};L.prototype.GetImage=function(a,c,d,e,g){var h=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);return H(Gc(h,a,c,d,e,g),N)};
L.prototype.Baseline=function(a,c,d,e,g){var h=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);return!!Hc(h,a,c,d,e,g)};L.prototype.RowAttributes=function(a,c,d){var e=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);return!!Ic(e,a,c,d)};
L.prototype.Orientation=function(a,c,d,e){var g=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);Jc(g,a,c,d,e)};L.prototype.ParagraphInfo=function(a,c,d,e){var g=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);Kc(g,a,c,d,e)};L.prototype.ParagraphIsLtr=function(){return!!Lc(this.Cf)};
L.prototype.GetUTF8Text=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return z(Mc(c,a))};L.prototype.SetLineSeparator=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);Nc(c,a)};L.prototype.SetParagraphSeparator=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);Oc(c,a)};L.prototype.Confidence=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return Pc(c,a)};
L.prototype.WordFontAttributes=function(a,c,d,e,g,h,k,m){var v=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);h&&"object"===typeof h&&(h=h.Cf);k&&"object"===typeof k&&(k=k.Cf);m&&"object"===typeof m&&(m=m.Cf);return z(Qc(v,a,c,d,e,g,h,k,m))};L.prototype.WordRecognitionLanguage=function(){return z(Rc(this.Cf))};L.prototype.WordDirection=function(){return Sc(this.Cf)};
L.prototype.WordIsFromDictionary=function(){return!!Tc(this.Cf)};L.prototype.WordIsNumeric=function(){return!!Uc(this.Cf)};L.prototype.HasBlamerInfo=function(){return!!Vc(this.Cf)};L.prototype.HasTruthString=function(){return!!Wc(this.Cf)};L.prototype.EquivalentToTruth=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);return!!Xc(c,a)};L.prototype.WordTruthUTF8Text=function(){return z(Yc(this.Cf))};L.prototype.WordNormedUTF8Text=function(){return z(Zc(this.Cf))};
L.prototype.WordLattice=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return z($c(c,a))};L.prototype.SymbolIsSuperscript=function(){return!!ad(this.Cf)};L.prototype.SymbolIsSubscript=function(){return!!bd(this.Cf)};L.prototype.SymbolIsDropcap=function(){return!!cd(this.Cf)};L.prototype.__destroy__=function(){dd(this.Cf)};function Uh(){throw"cannot construct a TextlineOrder, no constructor in IDL";}Uh.prototype=Object.create(G.prototype);Uh.prototype.constructor=Uh;Uh.prototype.Hf=Uh;
Uh.If={};b.TextlineOrder=Uh;Uh.prototype.__destroy__=function(){ed(this.Cf)};function Vh(){throw"cannot construct a ETEXT_DESC, no constructor in IDL";}Vh.prototype=Object.create(G.prototype);Vh.prototype.constructor=Vh;Vh.prototype.Hf=Vh;Vh.If={};b.ETEXT_DESC=Vh;Vh.prototype.__destroy__=function(){fd(this.Cf)};function O(){throw"cannot construct a PageIterator, no constructor in IDL";}O.prototype=Object.create(G.prototype);O.prototype.constructor=O;O.prototype.Hf=O;O.If={};b.PageIterator=O;
O.prototype.Begin=function(){gd(this.Cf)};O.prototype.RestartParagraph=function(){hd(this.Cf)};O.prototype.IsWithinFirstTextlineOfParagraph=function(){return!!jd(this.Cf)};O.prototype.RestartRow=function(){kd(this.Cf)};O.prototype.Next=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return!!ld(c,a)};O.prototype.IsAtBeginningOf=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return!!md(c,a)};
O.prototype.IsAtFinalElement=function(a,c){var d=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);return!!nd(d,a,c)};O.prototype.Cmp=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return od(c,a)};O.prototype.SetBoundingBoxComponents=function(a,c){var d=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);pd(d,a,c)};
O.prototype.BoundingBox=function(a,c,d,e,g,h){var k=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);h&&"object"===typeof h&&(h=h.Cf);return void 0===h?!!qd(k,a,c,d,e,g):!!rd(k,a,c,d,e,g,h)};
O.prototype.BoundingBoxInternal=function(a,c,d,e,g){var h=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);return!!sd(h,a,c,d,e,g)};O.prototype.Empty=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return!!td(c,a)};O.prototype.BlockType=function(){return ud(this.Cf)};O.prototype.BlockPolygon=function(){return H(vd(this.Cf),M)};
O.prototype.GetBinaryImage=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return H(wd(c,a),N)};O.prototype.GetImage=function(a,c,d,e,g){var h=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);return H(xd(h,a,c,d,e,g),N)};
O.prototype.Baseline=function(a,c,d,e,g){var h=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);return!!yd(h,a,c,d,e,g)};O.prototype.Orientation=function(a,c,d,e){var g=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);zd(g,a,c,d,e)};
O.prototype.ParagraphInfo=function(a,c,d,e){var g=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);Ad(g,a,c,d,e)};O.prototype.__destroy__=function(){Bd(this.Cf)};function Wh(){throw"cannot construct a WritingDirection, no constructor in IDL";}Wh.prototype=Object.create(G.prototype);Wh.prototype.constructor=Wh;Wh.prototype.Hf=Wh;Wh.If={};b.WritingDirection=Wh;Wh.prototype.__destroy__=function(){Cd(this.Cf)};
function Xh(a){a&&"object"===typeof a&&(a=a.Cf);this.Cf=Dd(a);Jh(Xh)[this.Cf]=this}Xh.prototype=Object.create(G.prototype);Xh.prototype.constructor=Xh;Xh.prototype.Hf=Xh;Xh.If={};b.WordChoiceIterator=Xh;Xh.prototype.Next=function(){return!!Ed(this.Cf)};Xh.prototype.GetUTF8Text=function(){return z(Fd(this.Cf))};Xh.prototype.Confidence=function(){return Gd(this.Cf)};Xh.prototype.__destroy__=function(){Hd(this.Cf)};function P(){throw"cannot construct a Box, no constructor in IDL";}P.prototype=Object.create(G.prototype);
P.prototype.constructor=P;P.prototype.Hf=P;P.If={};b.Box=P;P.prototype.get_x=P.prototype.Kg=function(){return Id(this.Cf)};Object.defineProperty(P.prototype,"x",{get:P.prototype.Kg});P.prototype.get_y=P.prototype.Lg=function(){return Jd(this.Cf)};Object.defineProperty(P.prototype,"y",{get:P.prototype.Lg});P.prototype.get_w=P.prototype.Jg=function(){return Kd(this.Cf)};Object.defineProperty(P.prototype,"w",{get:P.prototype.Jg});P.prototype.get_h=P.prototype.Ig=function(){return Ld(this.Cf)};
Object.defineProperty(P.prototype,"h",{get:P.prototype.Ig});P.prototype.get_refcount=P.prototype.Xf=function(){return Md(this.Cf)};Object.defineProperty(P.prototype,"refcount",{get:P.prototype.Xf});P.prototype.__destroy__=function(){Nd(this.Cf)};function Q(a,c,d){I();a=a&&"object"===typeof a?a.Cf:J(a);c=c&&"object"===typeof c?c.Cf:J(c);d&&"object"===typeof d&&(d=d.Cf);this.Cf=Od(a,c,d);Jh(Q)[this.Cf]=this}Q.prototype=Object.create(G.prototype);Q.prototype.constructor=Q;Q.prototype.Hf=Q;Q.If={};
b.TessPDFRenderer=Q;Q.prototype.BeginDocument=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);return!!Pd(c,a)};Q.prototype.AddImage=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return!!Qd(c,a)};Q.prototype.EndDocument=function(){return!!Rd(this.Cf)};Q.prototype.happy=function(){return!!Sd(this.Cf)};Q.prototype.file_extension=function(){return z(Td(this.Cf))};Q.prototype.title=Q.prototype.title=function(){return z(Ud(this.Cf))};Q.prototype.imagenum=function(){return Vd(this.Cf)};
Q.prototype.__destroy__=function(){Wd(this.Cf)};function Yh(){throw"cannot construct a PixaPtr, no constructor in IDL";}Yh.prototype=Object.create(G.prototype);Yh.prototype.constructor=Yh;Yh.prototype.Hf=Yh;Yh.If={};b.PixaPtr=Yh;Yh.prototype.__destroy__=function(){Xd(this.Cf)};function Zh(){throw"cannot construct a FloatPtr, no constructor in IDL";}Zh.prototype=Object.create(G.prototype);Zh.prototype.constructor=Zh;Zh.prototype.Hf=Zh;Zh.If={};b.FloatPtr=Zh;Zh.prototype.__destroy__=function(){Yd(this.Cf)};
function $h(a){a&&"object"===typeof a&&(a=a.Cf);this.Cf=Zd(a);Jh($h)[this.Cf]=this}$h.prototype=Object.create(G.prototype);$h.prototype.constructor=$h;$h.prototype.Hf=$h;$h.If={};b.ChoiceIterator=$h;$h.prototype.Next=function(){return!!$d(this.Cf)};$h.prototype.GetUTF8Text=function(){return z(ae(this.Cf))};$h.prototype.Confidence=function(){return be(this.Cf)};$h.prototype.__destroy__=function(){ce(this.Cf)};function ai(){throw"cannot construct a PixPtr, no constructor in IDL";}ai.prototype=Object.create(G.prototype);
ai.prototype.constructor=ai;ai.prototype.Hf=ai;ai.If={};b.PixPtr=ai;ai.prototype.__destroy__=function(){de(this.Cf)};function bi(){throw"cannot construct a UNICHARSET, no constructor in IDL";}bi.prototype=Object.create(G.prototype);bi.prototype.constructor=bi;bi.prototype.Hf=bi;bi.If={};b.UNICHARSET=bi;bi.prototype.get_script_from_script_id=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return z(ee(c,a))};
bi.prototype.get_script_id_from_name=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);return fe(c,a)};bi.prototype.get_script_table_size=function(){return ge(this.Cf)};bi.prototype.__destroy__=function(){he(this.Cf)};function ci(){throw"cannot construct a IntPtr, no constructor in IDL";}ci.prototype=Object.create(G.prototype);ci.prototype.constructor=ci;ci.prototype.Hf=ci;ci.If={};b.IntPtr=ci;ci.prototype.__destroy__=function(){ie(this.Cf)};
function di(){throw"cannot construct a Orientation, no constructor in IDL";}di.prototype=Object.create(G.prototype);di.prototype.constructor=di;di.prototype.Hf=di;di.If={};b.Orientation=di;di.prototype.__destroy__=function(){je(this.Cf)};function R(){throw"cannot construct a OSBestResult, no constructor in IDL";}R.prototype=Object.create(G.prototype);R.prototype.constructor=R;R.prototype.Hf=R;R.If={};b.OSBestResult=R;R.prototype.get_orientation_id=R.prototype.Uh=function(){return ke(this.Cf)};
Object.defineProperty(R.prototype,"orientation_id",{get:R.prototype.Uh});R.prototype.get_script_id=R.prototype.Xh=function(){return le(this.Cf)};Object.defineProperty(R.prototype,"script_id",{get:R.prototype.Xh});R.prototype.get_sconfidence=R.prototype.Wh=function(){return me(this.Cf)};Object.defineProperty(R.prototype,"sconfidence",{get:R.prototype.Wh});R.prototype.get_oconfidence=R.prototype.Th=function(){return ne(this.Cf)};Object.defineProperty(R.prototype,"oconfidence",{get:R.prototype.Th});
R.prototype.__destroy__=function(){oe(this.Cf)};function S(){throw"cannot construct a Boxa, no constructor in IDL";}S.prototype=Object.create(G.prototype);S.prototype.constructor=S;S.prototype.Hf=S;S.If={};b.Boxa=S;S.prototype.get_n=S.prototype.ag=function(){return pe(this.Cf)};Object.defineProperty(S.prototype,"n",{get:S.prototype.ag});S.prototype.get_nalloc=S.prototype.bg=function(){return qe(this.Cf)};Object.defineProperty(S.prototype,"nalloc",{get:S.prototype.bg});
S.prototype.get_refcount=S.prototype.Xf=function(){return re(this.Cf)};Object.defineProperty(S.prototype,"refcount",{get:S.prototype.Xf});S.prototype.get_box=S.prototype.Mh=function(){return H(se(this.Cf),ei)};Object.defineProperty(S.prototype,"box",{get:S.prototype.Mh});S.prototype.__destroy__=function(){te(this.Cf)};function T(){throw"cannot construct a PixColormap, no constructor in IDL";}T.prototype=Object.create(G.prototype);T.prototype.constructor=T;T.prototype.Hf=T;T.If={};b.PixColormap=T;
T.prototype.get_array=T.prototype.Kh=function(){return ue(this.Cf)};Object.defineProperty(T.prototype,"array",{get:T.prototype.Kh});T.prototype.get_depth=T.prototype.Rh=function(){return ve(this.Cf)};Object.defineProperty(T.prototype,"depth",{get:T.prototype.Rh});T.prototype.get_nalloc=T.prototype.bg=function(){return we(this.Cf)};Object.defineProperty(T.prototype,"nalloc",{get:T.prototype.bg});T.prototype.get_n=T.prototype.ag=function(){return xe(this.Cf)};Object.defineProperty(T.prototype,"n",{get:T.prototype.ag});
T.prototype.__destroy__=function(){ye(this.Cf)};function M(){throw"cannot construct a Pta, no constructor in IDL";}M.prototype=Object.create(G.prototype);M.prototype.constructor=M;M.prototype.Hf=M;M.If={};b.Pta=M;M.prototype.get_n=M.prototype.ag=function(){return ze(this.Cf)};Object.defineProperty(M.prototype,"n",{get:M.prototype.ag});M.prototype.get_nalloc=M.prototype.bg=function(){return Ae(this.Cf)};Object.defineProperty(M.prototype,"nalloc",{get:M.prototype.bg});
M.prototype.get_refcount=M.prototype.Xf=function(){return Be(this.Cf)};Object.defineProperty(M.prototype,"refcount",{get:M.prototype.Xf});M.prototype.get_x=M.prototype.Kg=function(){return H(Ce(this.Cf),Zh)};Object.defineProperty(M.prototype,"x",{get:M.prototype.Kg});M.prototype.get_y=M.prototype.Lg=function(){return H(De(this.Cf),Zh)};Object.defineProperty(M.prototype,"y",{get:M.prototype.Lg});M.prototype.__destroy__=function(){Ee(this.Cf)};
function N(){throw"cannot construct a Pix, no constructor in IDL";}N.prototype=Object.create(G.prototype);N.prototype.constructor=N;N.prototype.Hf=N;N.If={};b.Pix=N;N.prototype.get_w=N.prototype.Jg=function(){return Fe(this.Cf)};Object.defineProperty(N.prototype,"w",{get:N.prototype.Jg});N.prototype.get_h=N.prototype.Ig=function(){return Ge(this.Cf)};Object.defineProperty(N.prototype,"h",{get:N.prototype.Ig});N.prototype.get_d=N.prototype.Ph=function(){return He(this.Cf)};
Object.defineProperty(N.prototype,"d",{get:N.prototype.Ph});N.prototype.get_spp=N.prototype.Zh=function(){return Ie(this.Cf)};Object.defineProperty(N.prototype,"spp",{get:N.prototype.Zh});N.prototype.get_wpl=N.prototype.bi=function(){return Je(this.Cf)};Object.defineProperty(N.prototype,"wpl",{get:N.prototype.bi});N.prototype.get_refcount=N.prototype.Xf=function(){return Ke(this.Cf)};Object.defineProperty(N.prototype,"refcount",{get:N.prototype.Xf});N.prototype.get_xres=N.prototype.ci=function(){return Le(this.Cf)};
Object.defineProperty(N.prototype,"xres",{get:N.prototype.ci});N.prototype.get_yres=N.prototype.di=function(){return Me(this.Cf)};Object.defineProperty(N.prototype,"yres",{get:N.prototype.di});N.prototype.get_informat=N.prototype.Sh=function(){return Ne(this.Cf)};Object.defineProperty(N.prototype,"informat",{get:N.prototype.Sh});N.prototype.get_special=N.prototype.Yh=function(){return Oe(this.Cf)};Object.defineProperty(N.prototype,"special",{get:N.prototype.Yh});
N.prototype.get_text=N.prototype.$h=function(){return z(Pe(this.Cf))};Object.defineProperty(N.prototype,"text",{get:N.prototype.$h});N.prototype.get_colormap=N.prototype.Oh=function(){return H(Qe(this.Cf),T)};Object.defineProperty(N.prototype,"colormap",{get:N.prototype.Oh});N.prototype.get_data=N.prototype.Qh=function(){return Re(this.Cf)};Object.defineProperty(N.prototype,"data",{get:N.prototype.Qh});N.prototype.__destroy__=function(){Se(this.Cf)};
function fi(){throw"cannot construct a DoublePtr, no constructor in IDL";}fi.prototype=Object.create(G.prototype);fi.prototype.constructor=fi;fi.prototype.Hf=fi;fi.If={};b.DoublePtr=fi;fi.prototype.__destroy__=function(){Te(this.Cf)};function gi(){throw"cannot construct a Dawg, no constructor in IDL";}gi.prototype=Object.create(G.prototype);gi.prototype.constructor=gi;gi.prototype.Hf=gi;gi.If={};b.Dawg=gi;gi.prototype.__destroy__=function(){Ue(this.Cf)};
function ei(){throw"cannot construct a BoxPtr, no constructor in IDL";}ei.prototype=Object.create(G.prototype);ei.prototype.constructor=ei;ei.prototype.Hf=ei;ei.If={};b.BoxPtr=ei;ei.prototype.__destroy__=function(){Ve(this.Cf)};function X(){this.Cf=We();Jh(X)[this.Cf]=this}X.prototype=Object.create(G.prototype);X.prototype.constructor=X;X.prototype.Hf=X;X.If={};b.TessBaseAPI=X;X.prototype.Version=function(){return z(Xe(this.Cf))};
X.prototype.SetInputName=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);Ye(c,a)};X.prototype.GetInputName=function(){return z(Ze(this.Cf))};X.prototype.SetInputImage=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);$e(c,a)};X.prototype.GetInputImage=function(){return H(af(this.Cf),N)};X.prototype.GetSourceYResolution=function(){return bf(this.Cf)};X.prototype.GetDatapath=function(){return z(cf(this.Cf))};
X.prototype.SetOutputName=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);df(c,a)};X.prototype.SetVariable=X.prototype.SetVariable=function(a,c){var d=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);c=c&&"object"===typeof c?c.Cf:J(c);return!!ef(d,a,c)};X.prototype.SetDebugVariable=function(a,c){var d=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);c=c&&"object"===typeof c?c.Cf:J(c);return!!ff(d,a,c)};
X.prototype.GetIntVariable=function(a,c){var d=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);c&&"object"===typeof c&&(c=c.Cf);return!!gf(d,a,c)};X.prototype.GetBoolVariable=function(a,c){var d=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);c&&"object"===typeof c&&(c=c.Cf);return!!hf(d,a,c)};X.prototype.GetDoubleVariable=function(a,c){var d=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);c&&"object"===typeof c&&(c=c.Cf);return!!jf(d,a,c)};
X.prototype.GetStringVariable=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);return z(kf(c,a))};X.prototype.Init=function(a,c,d,e){void 0===d&&void 0!==e&&(d=3);var g=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);c=c&&"object"===typeof c?c.Cf:J(c);e=e&&"object"===typeof e?e.Cf:J(e);d&&"object"===typeof d&&(d=d.Cf);return void 0===d&&void 0!==e?pf(g,a,c,3,e):void 0===d?nf(g,a,c):void 0===e?of(g,a,c,d):pf(g,a,c,d,e)};X.prototype.GetInitLanguagesAsString=function(){return z(qf(this.Cf))};
X.prototype.InitForAnalysePage=function(){rf(this.Cf)};X.prototype.SaveParameters=function(){lf(this.Cf)};X.prototype.RestoreParameters=function(){mf(this.Cf)};X.prototype.ReadConfigFile=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);sf(c,a)};X.prototype.ReadDebugConfigFile=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);tf(c,a)};X.prototype.SetPageSegMode=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);uf(c,a)};X.prototype.GetPageSegMode=function(){return vf(this.Cf)};
X.prototype.TesseractRect=function(a,c,d,e,g,h,k){var m=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);h&&"object"===typeof h&&(h=h.Cf);k&&"object"===typeof k&&(k=k.Cf);return z(wf(m,a,c,d,e,g,h,k))};X.prototype.ClearAdaptiveClassifier=function(){xf(this.Cf)};
X.prototype.SetImage=function(a,c,d,e,g,h=1,k=0){var m=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);void 0===c||null===c?yf(m,a,h,k):zf(m,a,c,d,e,g,h,k)};X.prototype.SetImageFile=function(a=1,c=0){return Af(this.Cf,a,c)};X.prototype.SetSourceResolution=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);Bf(c,a)};
X.prototype.SetRectangle=function(a,c,d,e){var g=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);Cf(g,a,c,d,e)};X.prototype.GetThresholdedImage=function(){return H(Df(this.Cf),N)};X.prototype.WriteImage=function(a){Ef(this.Cf,a)};X.prototype.GetRegions=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return H(Hf(c,a),S)};
X.prototype.GetTextlines=function(a,c,d,e,g){var h=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);return void 0===d?H(If(h,a,c),S):void 0===e?H(_emscripten_bind_TessBaseAPI_GetTextlines_3(h,a,c,d),S):void 0===g?H(_emscripten_bind_TessBaseAPI_GetTextlines_4(h,a,c,d,e),S):H(Jf(h,a,c,d,e,g),S)};
X.prototype.GetStrips=function(a,c){var d=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);return H(Kf(d,a,c),S)};X.prototype.GetWords=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return H(Lf(c,a),S)};X.prototype.GetConnectedComponents=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return H(Mf(c,a),S)};
X.prototype.GetComponentImages=function(a,c,d,e,g,h,k){var m=this.Cf;a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);g&&"object"===typeof g&&(g=g.Cf);h&&"object"===typeof h&&(h=h.Cf);k&&"object"===typeof k&&(k=k.Cf);return void 0===g?H(Nf(m,a,c,d,e),S):void 0===h?H(_emscripten_bind_TessBaseAPI_GetComponentImages_5(m,a,c,d,e,g),S):void 0===k?H(_emscripten_bind_TessBaseAPI_GetComponentImages_6(m,a,c,d,e,g,h),S):H(Of(m,
a,c,d,e,g,h,k),S)};X.prototype.GetThresholdedImageScaleFactor=function(){return Pf(this.Cf)};X.prototype.AnalyseLayout=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return void 0===a?H(Qf(c),O):H(Rf(c,a),O)};X.prototype.Recognize=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return Sf(c,a)};X.prototype.FindLines=function(){return Ff(this.Cf)};X.prototype.GetGradient=function(){return Gf(this.Cf)};
X.prototype.ProcessPages=function(a,c,d,e){var g=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);c=c&&"object"===typeof c?c.Cf:J(c);d&&"object"===typeof d&&(d=d.Cf);e&&"object"===typeof e&&(e=e.Cf);return!!Tf(g,a,c,d,e)};
X.prototype.ProcessPage=function(a,c,d,e,g,h){var k=this.Cf;I();a&&"object"===typeof a&&(a=a.Cf);c&&"object"===typeof c&&(c=c.Cf);d=d&&"object"===typeof d?d.Cf:J(d);e=e&&"object"===typeof e?e.Cf:J(e);g&&"object"===typeof g&&(g=g.Cf);h&&"object"===typeof h&&(h=h.Cf);return!!Uf(k,a,c,d,e,g,h)};X.prototype.GetIterator=function(){return H(Vf(this.Cf),L)};X.prototype.GetUTF8Text=function(){return z(Wf(this.Cf))};
X.prototype.GetHOCRText=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return z(Xf(c,a))};X.prototype.GetTSVText=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return z(Yf(c,a))};X.prototype.GetBoxText=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return z(Zf(c,a))};X.prototype.GetUNLVText=function(){return z($f(this.Cf))};X.prototype.GetOsdText=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return z(ag(c,a))};X.prototype.MeanTextConf=function(){return bg(this.Cf)};
X.prototype.AllWordConfidences=function(){return H(cg(this.Cf),ci)};X.prototype.AdaptToWordStr=function(a,c){var d=this.Cf;I();a&&"object"===typeof a&&(a=a.Cf);c=c&&"object"===typeof c?c.Cf:J(c);return!!_emscripten_bind_TessBaseAPI_AdaptToWordStr_2(d,a,c)};X.prototype.Clear=function(){dg(this.Cf)};X.prototype.End=function(){eg(this.Cf)};X.prototype.ClearPersistentCache=function(){fg(this.Cf)};X.prototype.IsValidWord=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);return gg(c,a)};
X.prototype.IsValidCharacter=function(a){var c=this.Cf;I();a=a&&"object"===typeof a?a.Cf:J(a);return!!hg(c,a)};X.prototype.DetectOS=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return!!ig(c,a)};X.prototype.GetUnichar=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return z(jg(c,a))};X.prototype.GetDawg=function(a){var c=this.Cf;a&&"object"===typeof a&&(a=a.Cf);return H(kg(c,a),gi)};X.prototype.NumDawgs=function(){return lg(this.Cf)};X.prototype.oem=function(){return mg(this.Cf)};
X.prototype.__destroy__=function(){ng(this.Cf)};function Y(){this.Cf=og();Jh(Y)[this.Cf]=this}Y.prototype=Object.create(G.prototype);Y.prototype.constructor=Y;Y.prototype.Hf=Y;Y.If={};b.OSResults=Y;Y.prototype.print_scores=function(){pg(this.Cf)};Y.prototype.get_best_result=Y.prototype.Lh=function(){return H(qg(this.Cf),R)};Object.defineProperty(Y.prototype,"best_result",{get:Y.prototype.Lh});Y.prototype.get_unicharset=Y.prototype.ai=function(){return H(rg(this.Cf),bi)};
Object.defineProperty(Y.prototype,"unicharset",{get:Y.prototype.ai});Y.prototype.__destroy__=function(){sg(this.Cf)};function Z(){throw"cannot construct a Pixa, no constructor in IDL";}Z.prototype=Object.create(G.prototype);Z.prototype.constructor=Z;Z.prototype.Hf=Z;Z.If={};b.Pixa=Z;Z.prototype.get_n=Z.prototype.ag=function(){return tg(this.Cf)};Object.defineProperty(Z.prototype,"n",{get:Z.prototype.ag});Z.prototype.get_nalloc=Z.prototype.bg=function(){return ug(this.Cf)};
Object.defineProperty(Z.prototype,"nalloc",{get:Z.prototype.bg});Z.prototype.get_refcount=Z.prototype.Xf=function(){return vg(this.Cf)};Object.defineProperty(Z.prototype,"refcount",{get:Z.prototype.Xf});Z.prototype.get_pix=Z.prototype.Vh=function(){return H(wg(this.Cf),ai)};Object.defineProperty(Z.prototype,"pix",{get:Z.prototype.Vh});Z.prototype.get_boxa=Z.prototype.Nh=function(){return H(xg(this.Cf),S)};Object.defineProperty(Z.prototype,"boxa",{get:Z.prototype.Nh});Z.prototype.__destroy__=function(){yg(this.Cf)};
(function(){function a(){b.RIL_BLOCK=zg();b.RIL_PARA=Ag();b.RIL_TEXTLINE=Bg();b.RIL_WORD=Cg();b.RIL_SYMBOL=Dg();b.OEM_TESSERACT_ONLY=Eg();b.OEM_LSTM_ONLY=Fg();b.OEM_TESSERACT_LSTM_COMBINED=Gg();b.OEM_DEFAULT=Hg();b.OEM_COUNT=Ig();b.WRITING_DIRECTION_LEFT_TO_RIGHT=Jg();b.WRITING_DIRECTION_RIGHT_TO_LEFT=Kg();b.WRITING_DIRECTION_TOP_TO_BOTTOM=Lg();b.PT_UNKNOWN=Mg();b.PT_FLOWING_TEXT=Ng();b.PT_HEADING_TEXT=Og();b.PT_PULLOUT_TEXT=Pg();b.PT_EQUATION=Qg();b.PT_INLINE_EQUATION=Rg();b.PT_TABLE=Sg();b.PT_VERTICAL_TEXT=
Tg();b.PT_CAPTION_TEXT=Ug();b.PT_FLOWING_IMAGE=Vg();b.PT_HEADING_IMAGE=Wg();b.PT_PULLOUT_IMAGE=Xg();b.PT_HORZ_LINE=Yg();b.PT_VERT_LINE=Zg();b.PT_NOISE=$g();b.PT_COUNT=ah();b.DIR_NEUTRAL=bh();b.DIR_LEFT_TO_RIGHT=ch();b.DIR_RIGHT_TO_LEFT=dh();b.DIR_MIX=eh();b.JUSTIFICATION_UNKNOWN=fh();b.JUSTIFICATION_LEFT=gh();b.JUSTIFICATION_CENTER=hh();b.JUSTIFICATION_RIGHT=ih();b.TEXTLINE_ORDER_LEFT_TO_RIGHT=jh();b.TEXTLINE_ORDER_RIGHT_TO_LEFT=kh();b.TEXTLINE_ORDER_TOP_TO_BOTTOM=lh();b.ORIENTATION_PAGE_UP=mh();
b.ORIENTATION_PAGE_RIGHT=nh();b.ORIENTATION_PAGE_DOWN=oh();b.ORIENTATION_PAGE_LEFT=ph();b.PSM_OSD_ONLY=qh();b.PSM_AUTO_OSD=rh();b.PSM_AUTO_ONLY=sh();b.PSM_AUTO=th();b.PSM_SINGLE_COLUMN=uh();b.PSM_SINGLE_BLOCK_VERT_TEXT=vh();b.PSM_SINGLE_BLOCK=wh();b.PSM_SINGLE_LINE=xh();b.PSM_SINGLE_WORD=yh();b.PSM_CIRCLE_WORD=zh();b.PSM_SINGLE_CHAR=Ah();b.PSM_SPARSE_TEXT=Bh();b.PSM_SPARSE_TEXT_OSD=Ch();b.PSM_RAW_LINE=Dh();b.PSM_COUNT=Eh()}Ca?a():Aa.unshift(a)})();
Qh.prototype.getValue=function(a){return!!Xa(this.Cf+(a||0),"i8")};ci.prototype.getValue=function(a){return Xa(this.Cf+4*(a||0),"i32")};Zh.prototype.getValue=function(a){return Xa(this.Cf+4*(a||0),"float")};fi.prototype.getValue=function(a){return Xa(this.Cf+8*(a||0),"double")};ei.prototype.get=Yh.prototype.get=ai.prototype.get=function(a){return Xa(this.Cf+4*(a||0),"*")};function hi(){this.ig={}}hi.prototype.wrap=function(a,c){var d=Fb(4);Ya(d,0,"i32");return this.ig[a]=H(d,c)};
hi.prototype.bool=function(a){return this.wrap(a,Qh)};hi.prototype.i32=function(a){return this.wrap(a,ci)};hi.prototype.f32=function(a){return this.wrap(a,Zh)};hi.prototype.f64=function(a){return this.ig[a]=H(Fb(8),fi)};hi.prototype.peek=function(){var a={},c;for(c in this.ig)a[c]=this.ig[c].getValue();return a};hi.prototype.get=function(){var a={},c;for(c in this.ig)a[c]=this.ig[c].getValue(),Fh(this.ig[c].Cf);return a};
L.prototype.getBoundingBox=function(a){var c=new hi;this.BoundingBox(a,c.i32("x0"),c.i32("y0"),c.i32("x1"),c.i32("y1"));return c.get()};L.prototype.getBaseline=function(a){var c=new hi;a=!!this.Baseline(a,c.i32("x0"),c.i32("y0"),c.i32("x1"),c.i32("y1"));c=c.get();c.has_baseline=a;return c};L.prototype.getRowAttributes=function(){var a=new hi;this.RowAttributes(a.f32("row_height"),a.f32("descenders"),a.f32("ascenders"));return a.get()};
L.prototype.getWordFontAttributes=function(){var a=new hi,c=this.WordFontAttributes(a.bool("is_bold"),a.bool("is_italic"),a.bool("is_underlined"),a.bool("is_monospace"),a.bool("is_serif"),a.bool("is_smallcaps"),a.i32("pointsize"),a.i32("font_id"));a=a.get();a.font_name=c;return a};b.pointerHelper=hi;


  return TesseractCore.ready
}

);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = TesseractCore;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return TesseractCore; });
else if (typeof exports === 'object')
  exports["TesseractCore"] = TesseractCore;
