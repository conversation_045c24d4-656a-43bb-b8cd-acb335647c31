// Simple test to verify basic functionality
console.log('Testing basic Node.js functionality...');

// Test if required modules can be loaded
try {
  const fs = require('fs');
  const path = require('path');
  console.log('✅ Basic modules loaded successfully');
  
  // Check if test image exists
  const testImagePath = path.join(__dirname, 'test-images', 'captcha-test.png');
  if (fs.existsSync(testImagePath)) {
    console.log('✅ Test image found');
    const stats = fs.statSync(testImagePath);
    console.log(`📊 Image size: ${stats.size} bytes`);
  } else {
    console.log('❌ Test image not found at:', testImagePath);
  }
  
  // Test environment variables
  require('dotenv').config();
  if (process.env.OPENAI_API_KEY) {
    console.log('✅ OpenAI API key found');
  } else {
    console.log('❌ OpenAI API key not found');
  }
  
  console.log('🎉 Basic test completed successfully');
  
} catch (error) {
  console.error('❌ Error during basic test:', error.message);
}
