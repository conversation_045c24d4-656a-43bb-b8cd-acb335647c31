{"name": "captcha-agent", "version": "1.0.0", "description": "Agent IA pour résoudre les CAPTCHA visuels", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "ts-node src/test.ts", "test:comprehensive": "ts-node src/test-comprehensive.ts"}, "keywords": ["<PERSON><PERSON>a", "openai", "agent", "typescript"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.2", "dotenv": "^16.3.1", "express": "^4.18.2", "openai": "^4.20.1", "sharp": "^0.33.3", "tesseract.js": "^5.1.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.2"}}