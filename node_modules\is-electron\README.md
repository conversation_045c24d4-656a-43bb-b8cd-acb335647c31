# is-electron [![build status](https://travis-ci.org/cheton/is-electron.svg?branch=master)](https://travis-ci.org/cheton/is-electron) [![Coverage Status](https://coveralls.io/repos/github/cheton/is-electron/badge.svg?branch=master)](https://coveralls.io/github/cheton/is-electron?branch=master)

[![NPM](https://nodei.co/npm/is-electron.png?downloads=true&stars=true)](https://www.npmjs.com/package/is-electron)

Detect if running in Electron.

## Installation

```bash
npm install --save is-electron
```

## Usage

```js
import isElectron from 'is-electron';

console.log(isElectron());
```

## License

Copyright (c) 2016 Cheton Wu

Licensed under the [MIT License](LICENSE).
